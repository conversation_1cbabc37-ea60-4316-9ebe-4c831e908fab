import asyncio
import json
import os
import shutil
import time
from server import PromptServer
import comfy.utils
from aiohttp import web

# Constants
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
WEB_DIRECTORY = "web"
DIR_WEB = os.path.join(THIS_DIR, WEB_DIRECTORY)

# Remove old directories if they exist
OLD_DIRS = [
    os.path.abspath(f'{THIS_DIR}/../../web/extensions/task_monitor'),
]
for old_dir in OLD_DIRS:
    if os.path.exists(old_dir):
        shutil.rmtree(old_dir)

# Ensure web directory exists
if not os.path.exists(DIR_WEB):
    os.makedirs(DIR_WEB)

class TaskMonitorNode:
    NAME = "TaskMonitorNode"
    
    def __init__(self):
        self.current_task_id = None
        self.current_node_id = None
        self.current_node_type = None
        self.progress_value = 0
        self.progress_max = 0
        self.start_time = None
        self.total_execution_time = 0
        
        # 简化的状态跟踪，不拦截任何事件
        self.prompt_workflow = None
        self.total_nodes_in_prompt = 0
        self.executed_node_ids_set = set()
        self.last_executed_node_id = None

    def reset_workflow_progress(self):
        self.prompt_workflow = None
        self.total_nodes_in_prompt = 0
        self.executed_node_ids_set = set()
        self.last_executed_node_id = None
        self.progress_value = 0
        self.progress_max = 0
        self.start_time = None
        self.total_execution_time = 0

    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {},
            "hidden": {"prompt": "PROMPT", "extra_pnginfo": "EXTRA_PNGINFO"},
        }

    RETURN_TYPES = ()
    FUNCTION = "do_nothing"
    OUTPUT_NODE = True
    CATEGORY = "utils"

    def do_nothing(self, prompt=None, extra_pnginfo=None):
        return ()

# Create the global instance
TaskMonitorNode.instance = TaskMonitorNode()

# 简化的状态获取 - 直接从 ComfyUI 内部获取信息，不拦截事件
async def get_task_status(request):
    """Handle GET requests to /task_monitor/status."""
    try:
        server = PromptServer.instance
        queue = server.prompt_queue
        
        running_tasks, pending_tasks_queue = queue.get_current_queue()
        
        # 基本状态判断
        status = "idle"
        current_task_id = None
        
        if running_tasks:
            current_task_id = running_tasks[0][1] if len(running_tasks[0]) > 1 else None
            status = "running"
        elif pending_tasks_queue:
            status = "queued"
        
        # 简单的进度信息
        workflow_progress = {
            "total_nodes": 0,
            "executed_nodes": 0,
            "last_executed_node_id": None
        }
        
        if running_tasks and len(running_tasks[0]) > 2:
            workflow = running_tasks[0][2]
            if workflow:
                total_nodes = len([
                    key for key in workflow.keys() 
                    if key.isdigit() and 
                    workflow[key].get("class_type") not in ["TaskMonitorNode"]
                ])
                workflow_progress["total_nodes"] = total_nodes
        
        status_data = {
            "task_id": current_task_id,
            "status": status,
            "queue": {
                "running_count": len(running_tasks),
                "pending_count": len(pending_tasks_queue),
                "running": [],
                "pending": []
            },
            "current_task_progress": None,
            "workflow_progress": workflow_progress,
            "execution_time": 0,
            "error_info": None
        }

        # 填充队列信息
        for task_list, status_type in [(running_tasks, "running"), (pending_tasks_queue, "pending")]:
            for task_item in task_list:
                if len(task_item) > 1:
                    prompt_id = task_item[1]
                    status_data["queue"][status_type].append({
                        "prompt_id": prompt_id,
                        "nodes_in_prompt": len(task_item[2]) if len(task_item) > 2 else 0,
                        "client_id": task_item[4].get('client_id') if len(task_item) > 4 and isinstance(task_item[4], dict) else None
                    })

        return web.json_response(status_data)
        
    except Exception as e:
        print(f"[TaskMonitor] Error in get_task_status: {e}")
        # 返回安全的默认状态
        return web.json_response({
            "task_id": None,
            "status": "idle",
            "queue": {"running_count": 0, "pending_count": 0, "running": [], "pending": []},
            "current_task_progress": None,
            "workflow_progress": {"total_nodes": 0, "executed_nodes": 0, "last_executed_node_id": None},
            "execution_time": 0,
            "error_info": str(e)
        })

def register_routes():
    """只注册 API 路由，不设置任何钩子"""
    try:
        if hasattr(PromptServer.instance, "app") and PromptServer.instance.app is not None:
            # Add static file serving
            PromptServer.instance.app.router.add_static('/task_monitor', DIR_WEB)
            
            # Add API routes
            PromptServer.instance.app.add_routes([
                web.get('/task_monitor/status', get_task_status)
            ])
            print("[TaskMonitor] API routes registered (safe mode - no event hooks)")
        else:
            async def deferred_register():
                await asyncio.sleep(1)
                if hasattr(PromptServer.instance, "app") and PromptServer.instance.app is not None:
                    PromptServer.instance.app.router.add_static('/task_monitor', DIR_WEB)
                    PromptServer.instance.app.add_routes([
                        web.get('/task_monitor/status', get_task_status)
                    ])
                    print("[TaskMonitor] API routes registered (safe mode - deferred)")
            
            if hasattr(PromptServer.instance, "loop") and PromptServer.instance.loop is not None:
                PromptServer.instance.loop.create_task(deferred_register())
            else:
                try:
                    loop = asyncio.get_running_loop()
                    loop.create_task(deferred_register())
                except RuntimeError:
                    pass
    except Exception as e:
        print(f"[TaskMonitor] Error registering routes: {e}")

# 只注册路由，不设置任何钩子
register_routes()

# Node registration
NODE_CLASS_MAPPINGS = {
    TaskMonitorNode.NAME: TaskMonitorNode
}

NODE_DISPLAY_NAME_MAPPINGS = {
    TaskMonitorNode.NAME: "Task Monitor API Node (Safe Mode)"
}

# Export for ComfyUI
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS', 'WEB_DIRECTORY']

print("[TaskMonitor] Safe mode initialized - no event interception, API only")
