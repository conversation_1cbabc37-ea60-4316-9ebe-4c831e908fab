import asyncio
import json
import os
import shutil
import copy
import time
from server import PromptServer
from comfy import samplers
import comfy.utils
from aiohttp import web


# Constants
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
WEB_DIRECTORY = "web"  # This will be the directory for web resources
DIR_WEB = os.path.join(THIS_DIR, WEB_DIRECTORY)

# Remove old directories if they exist
OLD_DIRS = [
    os.path.abspath(f'{THIS_DIR}/../../web/extensions/task_monitor'),
]
for old_dir in OLD_DIRS:
    if os.path.exists(old_dir):
        shutil.rmtree(old_dir)

# Ensure web directory exists
if not os.path.exists(DIR_WEB):
    os.makedirs(DIR_WEB)

# Add web directory to ComfyUI's static files
WEB_DIRECTORY = os.path.join(THIS_DIR, "web")

class TaskMonitorNode:
    NAME = "TaskMonitorNode"

    def __init__(self):
        self.current_task_id = None
        self.current_node_id = None
        self.current_node_type = None
        self.progress_value = 0
        self.progress_max = 0
        self.last_status_message = None
        self.last_progress_message = None
        self.output_cache = {}  # Cache for storing outputs once a task is done
        self.is_interrupted = False  # 中断状态标记

        # New status for overall workflow progress
        self.prompt_workflow = None  # The actual workflow dictionary for the current_task_id
        self.total_nodes_in_prompt = 0
        self.executed_node_ids_set = set()  # Set of node IDs (as strings) that have started execution for current_task_id
        self.last_executed_node_id = None  # The most recent node_id from an 'executing' event (node != null)
        self.execution_list = None  # Reference to the current execution list

        self.start_time = None  # Track when execution starts
        self.total_execution_time = 0  # Total execution time in seconds

        # 只监听我们需要的事件，不包括停止相关事件
        self.event_handlers = {
            "status": self.on_status,
            "execution_start": self.on_execution_start,
            "executing": self.on_executing,
            "execution_cached": self.on_execution_cached,
            "progress": self.on_progress,
            "executed": self.on_executed,
            "execution_error": self.on_execution_error,
            # 注意：我们不处理中断事件，让它们直接通过
        }

    def reset_workflow_progress(self):
        self.prompt_workflow = None
        self.total_nodes_in_prompt = 0
        self.executed_node_ids_set = set()  # This will store string node IDs
        self.last_executed_node_id = None
        self.execution_list = None
        self.progress_value = 0
        self.progress_max = 0
        self.start_time = None
        self.total_execution_time = 0
        self.is_interrupted = False  # 重置中断状态

    def set_current_task(self, prompt_id, workflow_dict):
        if self.current_task_id != prompt_id:
            self.reset_workflow_progress()
            self.current_task_id = prompt_id
            self.prompt_workflow = workflow_dict
            if self.prompt_workflow:
                # Count only nodes that are not input/output nodes and are actually needed for execution
                self.total_nodes_in_prompt = len([
                    key for key in self.prompt_workflow.keys()
                    if key.isdigit() and
                    self.prompt_workflow[key].get("class_type") not in ["TaskMonitorNode"]
                ])
        elif not self.prompt_workflow and workflow_dict:
            self.prompt_workflow = workflow_dict
            if self.prompt_workflow:
                # Count only nodes that are not input/output nodes and are actually needed for execution
                self.total_nodes_in_prompt = len([
                    key for key in self.prompt_workflow.keys()
                    if key.isdigit() and
                    self.prompt_workflow[key].get("class_type") not in ["TaskMonitorNode"]
                ])

    def on_status(self, data):
        if not data.get("status"):
            return

        queue_info = data.get("status", {})
        exec_info = queue_info.get("exec_info", {})
        queue_remaining = exec_info.get("queue_remaining", 0)

        # get current task
        server = PromptServer.instance
        running_tasks, pending_tasks = server.prompt_queue.get_current_queue()
        if running_tasks:
            current_task = running_tasks[0]
            if len(current_task) > 2:
                prompt_id = current_task[1]
                workflow = current_task[2]
                if prompt_id != self.current_task_id:
                    self.reset_workflow_progress()
                    self.current_task_id = prompt_id
                    self.prompt_workflow = workflow
                    # count only nodes that are not input/output nodes and are actually needed for execution
                    self.total_nodes_in_prompt = len([
                        key for key in workflow.keys()
                        if key.isdigit() and
                        workflow[key].get("class_type") not in ["TaskMonitorNode"]
                    ])

    def on_execution_start(self, data):
        prompt_id = data.get("prompt_id")
        if not prompt_id:
            return

        # get current task
        server = PromptServer.instance
        running_tasks, _ = server.prompt_queue.get_current_queue()
        for task in running_tasks:
            if len(task) > 2 and task[1] == prompt_id:
                workflow = task[2]
                self.set_current_task(prompt_id, workflow)
                # record start time
                self.start_time = time.time()
                self.total_execution_time = 0
                self.is_interrupted = False  # 重置中断状态
                break

    def on_executing(self, data):
        node_id = data.get("node")
        prompt_id = data.get("prompt_id")

        if prompt_id == self.current_task_id:
            if node_id is not None:
                # convert node ID to string to maintain consistency
                str_node_id = str(node_id)
                # count only nodes that are not input/output nodes and are actually needed for execution
                if (self.prompt_workflow and
                    str_node_id in self.prompt_workflow and
                    self.prompt_workflow[str_node_id].get("class_type") not in ["TaskMonitorNode"]):

                    self.current_node_id = node_id
                    self.current_node_type = self.prompt_workflow[str_node_id].get("class_type")
                    self.last_executed_node_id = node_id

                    if str_node_id not in self.executed_node_ids_set:
                        self.executed_node_ids_set.add(str_node_id)
            else:
                # node is None - 可能是完成或中断
                # 检查服务器状态来判断是否被中断
                server = PromptServer.instance
                if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
                    self.is_interrupted = True
                    print("[TaskMonitor] Detected interruption via server state")
                    return

                # 如果没有中断信号，按正常完成处理
                if self.prompt_workflow and self.total_nodes_in_prompt > 0:
                    executed_count = len(self.executed_node_ids_set)
                    if executed_count >= self.total_nodes_in_prompt:
                        # 真正完成所有节点
                        valid_node_ids = [
                            key for key in self.prompt_workflow.keys()
                            if key.isdigit() and
                            self.prompt_workflow[key].get("class_type") not in ["TaskMonitorNode"]
                        ]
                        self.executed_node_ids_set = set(valid_node_ids)
                        print(f"[TaskMonitor] Workflow completed: {executed_count}/{self.total_nodes_in_prompt} nodes")

    def on_execution_cached(self, data):
        prompt_id = data.get("prompt_id")
        cached_nodes = data.get("nodes", [])

        if prompt_id == self.current_task_id:
            for node_id in cached_nodes:
                str_node_id = str(node_id)
                if (self.prompt_workflow and
                    str_node_id in self.prompt_workflow and
                    self.prompt_workflow[str_node_id].get("class_type") not in ["TaskMonitorNode"]):

                    if str_node_id not in self.executed_node_ids_set:
                        self.executed_node_ids_set.add(str_node_id)

    def on_progress(self, data):
        self.progress_value = data.get("value", 0)
        self.progress_max = data.get("max", 0)

        # update current node information
        node_id = data.get("node")
        if node_id and self.prompt_workflow:
            str_node_id = str(node_id)
            node_info = self.prompt_workflow.get(str_node_id)
            if node_info:
                self.current_node_type = node_info.get("class_type")
                self.current_node_id = node_id

    def on_executed(self, data):
        # calculate total execution time
        if self.start_time is not None:
            self.total_execution_time = time.time() - self.start_time
            self.start_time = None

    def on_execution_error(self, data):
        # task execution error
        self.last_status_message = f"Error: {data.get('exception_message', 'Unknown error')}"

    def handle_event(self, event_type, data):
        handler = self.event_handlers.get(event_type)
        if handler:
            try:
                handler(data)
            except Exception as e:
                print(f"[TaskMonitor] Error handling event {event_type}: {e}")

    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {},
            "hidden": {"prompt": "PROMPT", "extra_pnginfo": "EXTRA_PNGINFO"},
        }

    RETURN_TYPES = ()
    FUNCTION = "do_nothing"
    OUTPUT_NODE = True
    CATEGORY = "utils"

    def do_nothing(self, prompt=None, extra_pnginfo=None):
        return ()

# Create the global instance
TaskMonitorNode.instance = TaskMonitorNode()

# Server utilities
def get_param(request, param, default=None):
    """Get a parameter from a request query string."""
    return request.rel_url.query[param] if param in request.rel_url.query else default

def is_param_truthy(request, param):
    """Check if a parameter is explicitly true."""
    val = get_param(request, param)
    return val is not None and val.lower() not in ("0", "false", "no")

# Route handlers
async def get_task_status(request):
    """Handle GET requests to /task_monitor/status."""
    monitor_node = TaskMonitorNode.instance
    server = PromptServer.instance
    queue = server.prompt_queue

    running_tasks, pending_tasks_queue = queue.get_current_queue()
    current_processing_prompt_id = getattr(server, 'last_prompt_id', None)

    # Get the actual execution list if available
    total_nodes = monitor_node.total_nodes_in_prompt
    executed_nodes = len(monitor_node.executed_node_ids_set)

    # Calculate current execution time if task is running
    current_execution_time = monitor_node.total_execution_time
    if monitor_node.start_time is not None:
        current_execution_time = time.time() - monitor_node.start_time

    # If we don't have the total nodes count yet, try to get it from the current task
    if total_nodes == 0 and running_tasks:
        for task in running_tasks:
            if len(task) > 2:
                workflow = task[2]
                if workflow:
                    total_nodes = len([
                        key for key in workflow.keys()
                        if key.isdigit() and
                        workflow[key].get("class_type") not in ["TaskMonitorNode"]
                    ])
                    monitor_node.total_nodes_in_prompt = total_nodes
                    monitor_node.prompt_workflow = workflow
                break

    # 智能状态判断
    status = "idle"

    # 检查服务器中断状态
    if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
        status = "interrupted"
        monitor_node.is_interrupted = True
    elif monitor_node.is_interrupted:
        status = "interrupted"
    elif current_processing_prompt_id:
        history = queue.get_history(prompt_id=current_processing_prompt_id)
        if current_processing_prompt_id in history:
            task_history = history[current_processing_prompt_id]
            if task_history.get('status', {}).get('status_str') == 'success':
                status = "completed"
            elif task_history.get('status', {}).get('status_str') == 'error':
                status = "error"
        else:
            is_running = any(task[1] == current_processing_prompt_id for task in running_tasks)
            if is_running:
                status = "running"
            else:
                is_queued = any(task[1] == current_processing_prompt_id for task in pending_tasks_queue)
                if is_queued:
                    status = "queued"

    status_data = {
        "task_id": current_processing_prompt_id,
        "status": status,
        "queue": {
            "running_count": len(running_tasks),
            "pending_count": len(pending_tasks_queue),
            "running": [],
            "pending": []
        },
        "current_task_progress": None,
        "current_task_outputs": None,
        "error_info": None,
        "workflow_progress": {
            "total_nodes": total_nodes,
            "executed_nodes": executed_nodes,
            "last_executed_node_id": monitor_node.last_executed_node_id
        },
        "execution_time": current_execution_time,
        "is_interrupted": monitor_node.is_interrupted,
    }

    # Populate queue information
    for task_list, status_type in [(running_tasks, "running"), (pending_tasks_queue, "pending")]:
        for task_item in task_list:
            prompt_id = task_item[1]
            status_data["queue"][status_type].append({
                "prompt_id": prompt_id,
                "nodes_in_prompt": len(task_item[2]) if len(task_item) > 2 else 0,
                "client_id": task_item[4].get('client_id') if len(task_item) > 4 and isinstance(task_item[4], dict) else None
            })

    if not current_processing_prompt_id and running_tasks:
        current_processing_prompt_id = running_tasks[0][1]
        status_data["task_id"] = current_processing_prompt_id

    if current_processing_prompt_id and status == "running":
        status_data["current_task_progress"] = {
            "node_id": monitor_node.current_node_id,
            "node_type": monitor_node.current_node_type,
            "step": monitor_node.progress_value,
            "total_steps": monitor_node.progress_max,
            "text_message": monitor_node.last_progress_message
        }

    return web.json_response(status_data)

# global hooks
_original_progress_hook = None
_original_send_sync_method = None

# 定义不需要拦截的事件（停止/中断相关）
BYPASS_EVENTS = {
    'interrupt',                    # 中断信号
    'execution_interrupted',        # 执行中断
    'execution_stopped',           # 执行停止
    'execution_cancelled',         # 执行取消
    'cancel',                      # 取消命令
    'stop',                        # 停止命令
    'abort',                       # 中止命令
    'kill',                        # 强制停止
    'terminate',                   # 终止命令
}

# 智能进度钩子 - 检查中断状态
def task_monitor_progress_hook(value, total, preview_image):
    """Hook for progress updates with interrupt detection."""
    try:
        monitor = TaskMonitorNode.instance
        server = PromptServer.instance

        # 首先检查是否有中断信号
        if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
            monitor.is_interrupted = True
            # 有中断信号时，直接调用原始钩子，不做额外处理
            if _original_progress_hook:
                return _original_progress_hook(value, total, preview_image)
            return preview_image

        # 没有中断信号时，快速更新进度信息
        monitor.progress_value = value
        monitor.progress_max = total

        # 快速更新当前节点信息
        current_node_id = getattr(server, 'last_node_id', None)
        if current_node_id and monitor.prompt_workflow:
            str_node_id = str(current_node_id)
            if str_node_id in monitor.prompt_workflow:
                monitor.current_node_id = current_node_id
                monitor.current_node_type = monitor.prompt_workflow[str_node_id].get("class_type")

    except Exception as e:
        print(f"[TaskMonitor] Error in progress hook: {e}")

    # 始终调用原始钩子
    if _original_progress_hook:
        try:
            return _original_progress_hook(value, total, preview_image)
        except Exception as e:
            print(f"[TaskMonitor] Error in original_progress_hook: {e}")

    return preview_image

# 🔥 核心智能事件拦截器 - 选择性拦截
def smart_send_sync_interceptor(event, data, sid=None):
    """
    智能事件拦截器：
    - 对于停止/中断相关事件：完全不拦截，直接通过
    - 对于其他事件：先处理监控逻辑，再调用原始方法
    """
    try:
        # 🚨 关键：停止/中断事件直接通过，不做任何拦截
        if event in BYPASS_EVENTS:
            print(f"[TaskMonitor] BYPASS event: {event} - passing through directly")
            # 直接调用原始方法，不做任何监控处理
            if _original_send_sync_method:
                return _original_send_sync_method(event, data, sid)
            return None

        # 对于非关键事件，进行监控处理
        monitor = TaskMonitorNode.instance

        # 但是要快速处理，避免阻塞
        if event in monitor.event_handlers:
            try:
                monitor.handle_event(event, data)
            except Exception as e:
                print(f"[TaskMonitor] Error handling event {event}: {e}")

        # 调用原始方法
        if _original_send_sync_method:
            return _original_send_sync_method(event, data, sid)
        return None

    except Exception as e:
        print(f"[TaskMonitor] Critical error in smart interceptor for event {event}: {e}")
        # 出错时确保原始方法被调用
        if _original_send_sync_method:
            return _original_send_sync_method(event, data, sid)
        return None

def setup_hooks():
    global _original_progress_hook, _original_send_sync_method

    try:
        # 设置进度钩子
        if comfy.utils.PROGRESS_BAR_HOOK and comfy.utils.PROGRESS_BAR_HOOK != task_monitor_progress_hook:
            _original_progress_hook = comfy.utils.PROGRESS_BAR_HOOK

        comfy.utils.set_progress_bar_global_hook(task_monitor_progress_hook)

        # 🔥 设置智能事件拦截器
        server = PromptServer.instance
        if hasattr(server, 'send_sync') and not hasattr(server, '_smart_tm_interceptor_bound'):
            _original_send_sync_method = server.send_sync
            server._smart_tm_interceptor_bound = True  # 标记已绑定

            # 使用智能拦截器
            def new_send_sync(event, data, sid=None):
                return smart_send_sync_interceptor(event, data, sid)

            server.send_sync = new_send_sync
            print("[TaskMonitor] Smart event interceptor installed - bypass events:", list(BYPASS_EVENTS))

    except Exception as e:
        print(f"[TaskMonitor] Error setting up smart hooks: {e}")

def register_routes():
    try:
        if hasattr(PromptServer.instance, "app") and PromptServer.instance.app is not None:
            # Add static file serving
            PromptServer.instance.app.router.add_static('/task_monitor', DIR_WEB)

            # Add API routes
            PromptServer.instance.app.add_routes([
                web.get('/task_monitor/status', get_task_status)
            ])
            print("[TaskMonitor] Routes registered successfully")
        else:
            async def deferred_register():
                await asyncio.sleep(1)
                if hasattr(PromptServer.instance, "app") and PromptServer.instance.app is not None:
                    PromptServer.instance.app.router.add_static('/task_monitor', DIR_WEB)
                    PromptServer.instance.app.add_routes([
                        web.get('/task_monitor/status', get_task_status)
                    ])
                    print("[TaskMonitor] Routes registered successfully (deferred)")

            if hasattr(PromptServer.instance, "loop") and PromptServer.instance.loop is not None:
                PromptServer.instance.loop.create_task(deferred_register())
            else:
                try:
                    loop = asyncio.get_running_loop()
                    loop.create_task(deferred_register())
                except RuntimeError:
                    pass
    except Exception as e:
        print(f"[TaskMonitor] Error registering routes: {e}")

# 延迟设置钩子
async def deferred_setup():
    await asyncio.sleep(1.0)
    try:
        if PromptServer.instance:
            setup_hooks()
            print("[TaskMonitor] Smart selective interception started")
            print("[TaskMonitor] Events that will BYPASS interception:", list(BYPASS_EVENTS))
    except Exception as e:
        print(f"[TaskMonitor] Error in deferred setup: {e}")

# 注册路由
register_routes()

# 设置延迟初始化
try:
    loop = asyncio.get_running_loop()
    loop.create_task(deferred_setup())
except RuntimeError:
    if PromptServer.instance and hasattr(PromptServer.instance, "loop"):
        PromptServer.instance.loop.create_task(deferred_setup())

# Node registration
NODE_CLASS_MAPPINGS = {
    TaskMonitorNode.NAME: TaskMonitorNode
}

NODE_DISPLAY_NAME_MAPPINGS = {
    TaskMonitorNode.NAME: "Task Monitor API Node (Smart Selective Interception)"
}

# Export for ComfyUI
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS', 'WEB_DIRECTORY']

print("[TaskMonitor] Smart selective interception version loaded")