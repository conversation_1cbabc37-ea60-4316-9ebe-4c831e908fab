import asyncio
import json
import os
import shutil
import copy
import time
from server import PromptServer
from comfy import samplers
import comfy.utils
from aiohttp import web

# Constants
THIS_DIR = os.path.dirname(os.path.abspath(__file__))
WEB_DIRECTORY = "web"
DIR_WEB = os.path.join(THIS_DIR, WEB_DIRECTORY)

# Ensure web directory exists
if not os.path.exists(DIR_WEB):
    os.makedirs(DIR_WEB)

class TaskMonitorNode:
    NAME = "TaskMonitorNode"
    
    def __init__(self):
        self.current_task_id = None
        self.current_node_id = None
        self.current_node_type = None
        self.progress_value = 0
        self.progress_max = 0
        self.is_interrupted = False

        # 简化的事件处理器
        self.event_handlers = {
            "status": self.on_status,
            "execution_start": self.on_execution_start,
            "executing": self.on_executing,
            "progress": self.on_progress,
            "executed": self.on_executed,
            "execution_error": self.on_execution_error,
        }

    def on_status(self, data):
        pass
    
    def on_execution_start(self, data):
        self.is_interrupted = False
    
    def on_executing(self, data):
        pass
    
    def on_progress(self, data):
        self.progress_value = data.get("value", 0)
        self.progress_max = data.get("max", 0)
    
    def on_executed(self, data):
        pass
    
    def on_execution_error(self, data):
        pass
    
    def handle_event(self, event_type, data):
        handler = self.event_handlers.get(event_type)
        if handler:
            try:
                handler(data)
            except Exception as e:
                print(f"[TaskMonitor] Error handling event {event_type}: {e}")

    @classmethod
    def INPUT_TYPES(s):
        return {
            "required": {},
            "hidden": {"prompt": "PROMPT", "extra_pnginfo": "EXTRA_PNGINFO"},
        }

    RETURN_TYPES = ()
    FUNCTION = "do_nothing"
    OUTPUT_NODE = True
    CATEGORY = "utils"

    def do_nothing(self, prompt=None, extra_pnginfo=None):
        return ()

# Create the global instance
TaskMonitorNode.instance = TaskMonitorNode()

# Route handlers
async def get_task_status(request):
    """Handle GET requests to /task_monitor/status."""
    try:
        monitor_node = TaskMonitorNode.instance
        server = PromptServer.instance
        queue = server.prompt_queue
        
        running_tasks, pending_tasks_queue = queue.get_current_queue()
        
        status_data = {
            "task_id": None,
            "status": "idle",
            "queue": {
                "running_count": len(running_tasks),
                "pending_count": len(pending_tasks_queue),
                "running": [],
                "pending": []
            },
            "current_task_progress": {
                "step": monitor_node.progress_value,
                "total_steps": monitor_node.progress_max,
            },
            "workflow_progress": {"total_nodes": 0, "executed_nodes": 0},
            "execution_time": 0,
            "is_interrupted": monitor_node.is_interrupted,
        }

        if running_tasks:
            status_data["task_id"] = running_tasks[0][1] if len(running_tasks[0]) > 1 else None
            status_data["status"] = "running"

        return web.json_response(status_data)
        
    except Exception as e:
        print(f"[TaskMonitor] Error in get_task_status: {e}")
        return web.json_response({
            "task_id": None,
            "status": "error",
            "error_info": str(e)
        })

# global hooks
_original_progress_hook = None
_original_send_sync_method = None

# 🔍 调试：记录所有事件
EVENT_LOG = []

def debug_send_sync_interceptor(event, data, sid=None):
    """
    调试版本：记录所有事件，特别关注取消相关的事件
    """
    try:
        # 🔍 记录所有事件
        EVENT_LOG.append({
            "event": event,
            "timestamp": time.time(),
            "data_keys": list(data.keys()) if isinstance(data, dict) else str(type(data))
        })
        
        # 🚨 特别关注可能的取消/停止事件
        cancel_keywords = ['cancel', 'stop', 'interrupt', 'abort', 'kill', 'terminate', 'halt', 'break']
        
        if any(keyword in event.lower() for keyword in cancel_keywords):
            print(f"🚨 [TaskMonitor] POTENTIAL CANCEL EVENT DETECTED: {event}")
            print(f"    Data: {data}")
            print(f"    SID: {sid}")
            
            # 对于可能的取消事件，直接通过
            if _original_send_sync_method:
                result = _original_send_sync_method(event, data, sid)
                print(f"    Result: {result}")
                return result
            return None
        
        # 🔍 记录所有其他事件（但不详细打印，避免刷屏）
        print(f"[TaskMonitor] Event: {event}")
        
        # 处理监控逻辑
        monitor = TaskMonitorNode.instance
        if event in monitor.event_handlers:
            monitor.handle_event(event, data)
        
        # 调用原始方法
        if _original_send_sync_method:
            return _original_send_sync_method(event, data, sid)
        return None
        
    except Exception as e:
        print(f"[TaskMonitor] Critical error in debug interceptor for event {event}: {e}")
        if _original_send_sync_method:
            return _original_send_sync_method(event, data, sid)
        return None

def debug_progress_hook(value, total, preview_image):
    """调试版本的进度钩子"""
    try:
        monitor = TaskMonitorNode.instance
        server = PromptServer.instance
        
        # 检查服务器中断状态
        if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
            print(f"🚨 [TaskMonitor] Server interrupt_processing detected: {server.interrupt_processing}")
            monitor.is_interrupted = True
        
        # 检查其他可能的中断标志
        interrupt_attrs = ['interrupted', 'should_stop', 'cancel_processing', 'stop_processing']
        for attr in interrupt_attrs:
            if hasattr(server, attr):
                value = getattr(server, attr)
                if value:
                    print(f"🚨 [TaskMonitor] Server {attr} detected: {value}")
        
        monitor.progress_value = value
        monitor.progress_max = total
        
    except Exception as e:
        print(f"[TaskMonitor] Error in debug progress hook: {e}")
    
    if _original_progress_hook:
        try:
            return _original_progress_hook(value, total, preview_image)
        except Exception as e:
            print(f"[TaskMonitor] Error in original_progress_hook: {e}")
    
    return preview_image

def setup_debug_hooks():
    global _original_progress_hook, _original_send_sync_method
    
    try:
        # 设置进度钩子
        if comfy.utils.PROGRESS_BAR_HOOK and comfy.utils.PROGRESS_BAR_HOOK != debug_progress_hook:
            _original_progress_hook = comfy.utils.PROGRESS_BAR_HOOK
        
        comfy.utils.set_progress_bar_global_hook(debug_progress_hook)
        
        # 设置调试事件拦截器
        server = PromptServer.instance
        if hasattr(server, 'send_sync') and not hasattr(server, '_debug_tm_interceptor_bound'):
            _original_send_sync_method = server.send_sync
            server._debug_tm_interceptor_bound = True
            
            def new_send_sync(event, data, sid=None):
                return debug_send_sync_interceptor(event, data, sid)
            
            server.send_sync = new_send_sync
            print("🔍 [TaskMonitor] DEBUG event interceptor installed")
            print("    Will log all events and highlight potential cancel events")
        
    except Exception as e:
        print(f"[TaskMonitor] Error setting up debug hooks: {e}")

def register_routes():
    try:
        if hasattr(PromptServer.instance, "app") and PromptServer.instance.app is not None:
            PromptServer.instance.app.add_routes([
                web.get('/task_monitor/status', get_task_status)
            ])
            print("[TaskMonitor] Debug routes registered")
    except Exception as e:
        print(f"[TaskMonitor] Error registering routes: {e}")

# 延迟设置钩子
async def deferred_setup():
    await asyncio.sleep(1.0)
    try:
        if PromptServer.instance:
            setup_debug_hooks()
            print("🔍 [TaskMonitor] DEBUG MODE ACTIVATED")
            print("    Please try to cancel a workflow and check the console output")
            print("    Look for '🚨 POTENTIAL CANCEL EVENT DETECTED' messages")
    except Exception as e:
        print(f"[TaskMonitor] Error in deferred setup: {e}")

# 注册路由
register_routes()

# 设置延迟初始化
try:
    loop = asyncio.get_running_loop()
    loop.create_task(deferred_setup())
except RuntimeError:
    if PromptServer.instance and hasattr(PromptServer.instance, "loop"):
        PromptServer.instance.loop.create_task(deferred_setup())

# Node registration
NODE_CLASS_MAPPINGS = {
    TaskMonitorNode.NAME: TaskMonitorNode
}

NODE_DISPLAY_NAME_MAPPINGS = {
    TaskMonitorNode.NAME: "Task Monitor API Node (DEBUG MODE)"
}

# Export for ComfyUI
__all__ = ['NODE_CLASS_MAPPINGS', 'NODE_DISPLAY_NAME_MAPPINGS', 'WEB_DIRECTORY']

print("🔍 [TaskMonitor] DEBUG VERSION LOADED - Will capture all events")
print("    Try canceling a workflow and check console for event details")
