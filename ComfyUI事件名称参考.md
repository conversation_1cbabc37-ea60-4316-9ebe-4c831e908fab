# ComfyUI 事件名称参考

## 🔍 可能的取消/停止事件名称

基于 ComfyUI 的源码分析，以下是可能的取消/停止相关事件名称：

### 🚨 高优先级（最可能的取消事件）

```python
# 这些是最可能的取消事件名称
LIKELY_CANCEL_EVENTS = {
    'interrupt',                    # 中断信号
    'execution_interrupted',        # 执行中断
    'queue_prompt_interrupt',       # 队列提示中断
    'free',                        # 释放资源
    'clear',                       # 清除
    'reset',                       # 重置
}
```

### 📋 中等优先级

```python
POSSIBLE_CANCEL_EVENTS = {
    'execution_stopped',           # 执行停止
    'execution_cancelled',         # 执行取消
    'cancel',                      # 取消命令
    'stop',                        # 停止命令
    'abort',                       # 中止命令
    'kill',                        # 强制停止
    'terminate',                   # 终止命令
    'halt',                        # 暂停
    'break',                       # 中断
}
```

### 🔍 低优先级（不太可能但值得检查）

```python
OTHER_EVENTS = {
    'queue_clear',                 # 清除队列
    'prompt_clear',                # 清除提示
    'execution_clear',             # 清除执行
    'workflow_stop',               # 工作流停止
    'task_cancel',                 # 任务取消
    'process_interrupt',           # 进程中断
}
```

## 🧪 调试步骤

### 1. 应用调试版本

```bash
python 快速调试TaskMonitor.py
```
选择 "1. 应用调试版本"

### 2. 重启 ComfyUI

完全关闭并重新启动 ComfyUI

### 3. 测试取消功能

1. 启动一个长时间运行的工作流（比如高分辨率图像生成）
2. 在工作流运行过程中，点击 ComfyUI 界面上的 **"取消当前任务"** 按钮
3. 观察控制台输出

### 4. 查找关键信息

在控制台中寻找以下信息：

```
🚨 [TaskMonitor] POTENTIAL CANCEL EVENT DETECTED: [事件名称]
    Data: [事件数据]
    SID: [会话ID]
    Result: [结果]
```

### 5. 分析结果

将控制台输出发送给我，我会分析：
- 实际的取消事件名称
- 事件数据结构
- 为什么没有被正确绕过

## 🔧 常见的 ComfyUI 事件模式

基于 ComfyUI 的架构，事件通常遵循以下模式：

### WebSocket 事件
```python
# 客户端到服务器
'queue_prompt'          # 队列提示
'queue_prompt_interrupt' # 中断队列提示 ⭐ 很可能是这个
'get_queue'             # 获取队列
'get_history'           # 获取历史

# 服务器到客户端  
'status'                # 状态更新
'progress'              # 进度更新
'executing'             # 执行中
'executed'              # 已执行
'execution_start'       # 执行开始
'execution_error'       # 执行错误
```

### 内部事件
```python
# 可能的内部事件
'interrupt_processing'   # 中断处理 ⭐ 很可能
'free_memory'           # 释放内存
'soft_empty_cache'      # 软清空缓存
'unload_models'         # 卸载模型
```

## 🎯 最可能的候选事件

根据 ComfyUI 的常见模式，取消任务最可能触发的事件是：

1. **`queue_prompt_interrupt`** - 队列提示中断
2. **`interrupt_processing`** - 中断处理
3. **`free`** - 释放资源
4. **`interrupt`** - 简单中断信号

## 📝 调试输出示例

成功捕获取消事件时，您应该看到类似这样的输出：

```
🔍 [TaskMonitor] DEBUG MODE ACTIVATED
[TaskMonitor] Event: execution_start
[TaskMonitor] Event: executing
[TaskMonitor] Event: progress
🚨 [TaskMonitor] POTENTIAL CANCEL EVENT DETECTED: queue_prompt_interrupt
    Data: {'prompt_id': '12345', 'node': None}
    SID: None
    Result: None
```

## 🛠️ 修复策略

一旦确定了正确的事件名称，我们将：

1. **更新 BYPASS_EVENTS** - 添加正确的事件名称
2. **测试验证** - 确认取消功能正常工作
3. **创建最终版本** - 提供完美的解决方案

## ⚠️ 注意事项

- 调试版本会输出大量信息，仅用于测试
- 测试完成后请恢复原始版本
- 如果没有看到取消事件，可能需要检查 ComfyUI 版本兼容性

请按照上述步骤进行调试，然后将控制台输出发送给我分析！
