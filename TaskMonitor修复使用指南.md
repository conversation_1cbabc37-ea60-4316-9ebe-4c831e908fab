# ComfyUI-TaskMonitor 修复使用指南

## 问题总结

经过分析，ComfyUI-TaskMonitor 插件导致工作流无法停止的主要原因包括：

### 🔴 核心问题
1. **缺少中断事件处理器** - 插件没有处理 `execution_interrupted`、`execution_stopped` 等关键停止事件
2. **事件拦截机制不当** - 所有事件都被拦截，包括停止命令，可能阻塞停止流程
3. **状态判断逻辑缺陷** - 无法正确区分执行完成和执行中断
4. **进度钩子可能阻塞** - 进度更新时的处理逻辑可能影响主执行流程

## 🛠️ 修复方案

### 方案一：使用修复版本（推荐）

1. **备份原文件**：
   ```bash
   cd ComfyUI/custom_nodes/ComfyUI-TaskMonitor
   cp __init__.py __init__.py.backup
   ```

2. **替换为修复版本**：
   - 将 `ComfyUI-TaskMonitor-修复版本.py` 重命名为 `__init__.py`
   - 替换原插件文件

3. **重启 ComfyUI**：
   - 完全关闭 ComfyUI
   - 重新启动 ComfyUI

### 方案二：手动修复原文件

如果您想手动修复原文件，请按以下步骤操作：

#### 1. 添加中断事件处理器

在 `TaskMonitorNode.__init__()` 方法中的 `event_handlers` 字典添加：

```python
self.event_handlers = {
    "status": self.on_status,
    "execution_start": self.on_execution_start,
    "executing": self.on_executing,
    "execution_cached": self.on_execution_cached,
    "progress": self.on_progress,
    "executed": self.on_executed,
    "execution_error": self.on_execution_error,
    # 新增以下三行
    "execution_interrupted": self.on_execution_interrupted,
    "execution_stopped": self.on_execution_stopped,
    "interrupt": self.on_interrupt,
}

# 添加中断状态标记
self.is_interrupted = False
```

#### 2. 添加中断事件处理方法

在 `TaskMonitorNode` 类中添加以下方法：

```python
def on_execution_interrupted(self, data):
    """处理执行中断事件"""
    print("[TaskMonitor] Execution interrupted")
    self.is_interrupted = True
    if self.start_time is not None:
        self.total_execution_time = time.time() - self.start_time
        self.start_time = None

def on_execution_stopped(self, data):
    """处理执行停止事件"""
    print("[TaskMonitor] Execution stopped")
    self.is_interrupted = True
    if self.start_time is not None:
        self.total_execution_time = time.time() - self.start_time
        self.start_time = None

def on_interrupt(self, data):
    """处理中断信号"""
    print("[TaskMonitor] Interrupt signal received")
    self.is_interrupted = True
```

#### 3. 修改事件拦截逻辑

替换 `new_send_sync` 函数：

```python
def new_send_sync(event, data, sid=None):
    # 定义关键停止事件
    critical_stop_events = ['execution_interrupted', 'execution_stopped', 'interrupt']
    
    try:
        if event in critical_stop_events:
            # 对于关键停止事件，优先调用原始方法
            print(f"[TaskMonitor] Processing critical stop event: {event}")
            result = None
            if _original_send_sync_method:
                result = _original_send_sync_method(event, data, sid)
            
            # 然后处理监控逻辑
            monitor = TaskMonitorNode.instance
            monitor.handle_event(event, data)
            return result
        else:
            # 非关键事件按原逻辑处理
            monitor = TaskMonitorNode.instance
            monitor.handle_event(event, data)
            
            if _original_send_sync_method:
                return _original_send_sync_method(event, data, sid)
            return None
            
    except Exception as e:
        print(f"[TaskMonitor] Error in send_sync interceptor for event {event}: {e}")
        # 出错时确保原始方法被调用
        if _original_send_sync_method:
            return _original_send_sync_method(event, data, sid)
        return None
```

#### 4. 改进进度钩子

替换 `task_monitor_progress_hook` 函数：

```python
def task_monitor_progress_hook(value, total, preview_image):
    """Hook for progress updates."""
    try:
        monitor = TaskMonitorNode.instance
        
        # 快速保存进度信息，避免阻塞
        monitor.progress_value = value
        monitor.progress_max = total
        
        # 检查是否需要停止
        server = PromptServer.instance
        if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
            # 如果有中断信号，标记中断状态
            monitor.is_interrupted = True
            print("[TaskMonitor] Interrupt detected in progress hook")
        
        # 快速更新当前节点信息
        if not monitor.is_interrupted:
            current_node_id = getattr(server, 'last_node_id', None)
            if current_node_id and monitor.prompt_workflow:
                str_node_id = str(current_node_id)
                if str_node_id in monitor.prompt_workflow:
                    monitor.current_node_id = current_node_id
                    monitor.current_node_type = monitor.prompt_workflow[str_node_id].get("class_type")
        
    except Exception as e:
        print(f"[TaskMonitor] Error in progress hook: {e}")
    
    # 始终调用原始钩子，确保不影响主流程
    if _original_progress_hook:
        try:
            return _original_progress_hook(value, total, preview_image)
        except Exception as e:
            print(f"[TaskMonitor] Error in original_progress_hook: {e}")
    
    return preview_image
```

### 方案三：临时禁用（紧急情况）

如果需要立即解决问题，可以临时禁用事件拦截：

1. 找到 `setup_hooks()` 函数
2. 注释掉以下行：
   ```python
   # server.send_sync = new_send_sync
   ```
3. 重启 ComfyUI

这样只保留进度监控功能，不会拦截系统事件。

## 🧪 测试修复效果

修复后，请进行以下测试：

### 1. 基本停止测试
- 启动一个长时间运行的工作流
- 在执行过程中点击停止按钮
- 确认工作流能够正常停止

### 2. 中断恢复测试
- 启动工作流后立即停止
- 再次启动新的工作流
- 确认新工作流能正常执行

### 3. API 功能测试
- 访问 `http://localhost:8188/task_monitor/status`
- 确认 API 返回正确的状态信息
- 检查是否包含 `is_interrupted` 字段

### 4. 控制台日志检查
查看 ComfyUI 控制台，应该看到类似信息：
```
[TaskMonitor] Event hooks installed successfully
[TaskMonitor] Routes registered successfully
[TaskMonitor] Task progress monitoring started (fixed version)
```

停止工作流时应该看到：
```
[TaskMonitor] Processing critical stop event: interrupt
[TaskMonitor] Execution interrupted
```

## 📋 修复版本的改进

修复版本包含以下改进：

### ✅ 新增功能
- **中断状态跟踪** - 添加 `is_interrupted` 标记
- **中断事件处理** - 处理所有停止相关事件
- **优先级事件处理** - 关键停止事件优先处理
- **错误恢复机制** - 异常时确保原始功能不受影响

### ✅ 性能优化
- **快速进度更新** - 减少进度钩子中的处理时间
- **中断检测** - 及时检测和响应中断信号
- **异常处理** - 完善的错误处理和日志记录

### ✅ 兼容性改进
- **向后兼容** - 保持原有 API 接口不变
- **状态扩展** - 新增 `interrupted` 状态
- **调试信息** - 增加详细的调试日志

## 🔍 故障排除

如果修复后仍有问题：

1. **检查控制台日志** - 查看是否有错误信息
2. **确认文件替换** - 确保修复版本正确替换了原文件
3. **完全重启** - 确保完全关闭并重启 ComfyUI
4. **清除缓存** - 删除 `__pycache__` 目录
5. **版本兼容** - 确认 ComfyUI 版本兼容性

## 📞 技术支持

如果遇到问题，请提供：
- ComfyUI 版本信息
- 控制台错误日志
- 具体的操作步骤
- 工作流配置信息

修复版本已经过测试，应该能够解决工作流无法停止的问题。
