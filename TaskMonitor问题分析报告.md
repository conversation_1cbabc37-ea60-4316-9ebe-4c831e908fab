# ComfyUI-TaskMonitor 插件导致工作流无法停止问题分析

## 问题描述
ComfyUI-TaskMonitor 插件在运行时可能导致 ComfyUI 工作流无法手动停止的问题。

## 潜在问题分析

### 1. 🔴 **事件拦截机制问题**

**问题位置**: `setup_hooks()` 函数 (第394-422行)

```python
def new_send_sync(event, data, sid=None):
    # first handle the event
    monitor = TaskMonitorNode.instance
    try:
        monitor.handle_event(event, data)
    except Exception as e:
        print(f"[TaskMonitor] Error handling event {event}: {e}")
    
    # then call the original method
    return _original_send_sync_method(event, data, sid)
```

**潜在问题**:
- 插件拦截了所有 `send_sync` 事件，包括停止命令
- 如果 `monitor.handle_event()` 出现异常或阻塞，可能影响原始事件处理
- 缺少对关键事件（如停止、中断）的特殊处理

### 2. 🔴 **缺少停止事件处理**

**问题位置**: `event_handlers` 定义 (第57-65行)

```python
self.event_handlers = {
    "status": self.on_status,
    "execution_start": self.on_execution_start,
    "executing": self.on_executing,
    "execution_cached": self.on_execution_cached,
    "progress": self.on_progress,
    "executed": self.on_executed,
    "execution_error": self.on_execution_error
}
```

**潜在问题**:
- **缺少 `execution_interrupted` 事件处理器**
- **缺少 `execution_stopped` 事件处理器**
- 这些事件对于正确处理工作流停止至关重要

### 3. 🔴 **进度钩子可能阻塞**

**问题位置**: `task_monitor_progress_hook()` 函数 (第357-381行)

```python
def task_monitor_progress_hook(value, total, preview_image):
    monitor = TaskMonitorNode.instance
    server = PromptServer.instance
    
    # save progress information
    monitor.progress_value = value
    monitor.progress_max = total
    
    # update current node information
    current_node_id = getattr(server, 'last_node_id', None)
    # ... 更多处理逻辑
```

**潜在问题**:
- 进度钩子在每次进度更新时都会执行
- 如果处理逻辑耗时过长，可能阻塞主执行流程
- 缺少对停止信号的检查

### 4. 🔴 **状态管理不完整**

**问题位置**: `on_executing()` 方法 (第144-173行)

```python
def on_executing(self, data):
    node_id = data.get("node")
    prompt_id = data.get("prompt_id")
    
    if prompt_id == self.current_task_id:
        if node_id is not None:
            # 处理节点执行
        else:
            # node is None means execution is completed
            # 强制标记所有节点为已执行
            self.executed_node_ids_set = set(valid_node_ids)
```

**潜在问题**:
- 当 `node_id` 为 `None` 时，插件假设执行完成
- 但 `node_id` 为 `None` 也可能表示执行被中断
- 缺少对中断状态的正确识别

## 🛠️ 解决方案

### 1. 添加停止事件处理器

```python
self.event_handlers = {
    "status": self.on_status,
    "execution_start": self.on_execution_start,
    "executing": self.on_executing,
    "execution_cached": self.on_execution_cached,
    "progress": self.on_progress,
    "executed": self.on_executed,
    "execution_error": self.on_execution_error,
    "execution_interrupted": self.on_execution_interrupted,  # 新增
    "execution_stopped": self.on_execution_stopped,          # 新增
}

def on_execution_interrupted(self, data):
    """处理执行中断事件"""
    print("[TaskMonitor] Execution interrupted")
    self.reset_workflow_progress()

def on_execution_stopped(self, data):
    """处理执行停止事件"""
    print("[TaskMonitor] Execution stopped")
    self.reset_workflow_progress()
```

### 2. 改进事件拦截机制

```python
def new_send_sync(event, data, sid=None):
    # 对于关键停止事件，优先处理原始方法
    critical_events = ['execution_interrupted', 'execution_stopped', 'interrupt']
    
    if event in critical_events:
        # 先调用原始方法确保停止命令被处理
        result = _original_send_sync_method(event, data, sid)
        # 然后处理监控逻辑
        try:
            monitor.handle_event(event, data)
        except Exception as e:
            print(f"[TaskMonitor] Error handling event {event}: {e}")
        return result
    else:
        # 非关键事件按原逻辑处理
        try:
            monitor.handle_event(event, data)
        except Exception as e:
            print(f"[TaskMonitor] Error handling event {event}: {e}")
        return _original_send_sync_method(event, data, sid)
```

### 3. 优化进度钩子

```python
def task_monitor_progress_hook(value, total, preview_image):
    try:
        monitor = TaskMonitorNode.instance
        
        # 快速保存进度信息，避免阻塞
        monitor.progress_value = value
        monitor.progress_max = total
        
        # 检查是否需要停止
        server = PromptServer.instance
        if hasattr(server, 'interrupt_processing') and server.interrupt_processing:
            # 如果有中断信号，直接返回
            if _original_progress_hook:
                return _original_progress_hook(value, total, preview_image)
            return preview_image
        
        # 其他处理逻辑...
        
    except Exception as e:
        print(f"[TaskMonitor] Error in progress hook: {e}")
    
    # 始终调用原始钩子
    if _original_progress_hook:
        return _original_progress_hook(value, total, preview_image)
    return preview_image
```

### 4. 改进执行状态判断

```python
def on_executing(self, data):
    node_id = data.get("node")
    prompt_id = data.get("prompt_id")
    
    if prompt_id == self.current_task_id:
        if node_id is not None:
            # 正常节点执行
            str_node_id = str(node_id)
            if (self.prompt_workflow and 
                str_node_id in self.prompt_workflow and 
                self.prompt_workflow[str_node_id].get("class_type") not in ["TaskMonitorNode"]):
                
                self.current_node_id = node_id
                self.current_node_type = self.prompt_workflow[str_node_id].get("class_type")
                self.last_executed_node_id = node_id
                
                if str_node_id not in self.executed_node_ids_set:
                    self.executed_node_ids_set.add(str_node_id)
        else:
            # node_id 为 None 可能是完成或中断
            # 检查是否真的完成了所有节点
            if self.prompt_workflow and self.total_nodes_in_prompt > 0:
                executed_count = len(self.executed_node_ids_set)
                if executed_count >= self.total_nodes_in_prompt:
                    # 真正完成
                    valid_node_ids = [
                        key for key in self.prompt_workflow.keys() 
                        if key.isdigit() and 
                        self.prompt_workflow[key].get("class_type") not in ["TaskMonitorNode"]
                    ]
                    self.executed_node_ids_set = set(valid_node_ids)
                else:
                    # 可能是中断，不要强制标记为完成
                    print(f"[TaskMonitor] Execution may be interrupted: {executed_count}/{self.total_nodes_in_prompt} nodes completed")
```

## 🔧 临时解决方案

如果需要立即解决问题，可以：

1. **禁用事件拦截**：注释掉 `setup_hooks()` 中的 `server.send_sync = new_send_sync` 行
2. **仅保留进度监控**：只使用进度钩子，不拦截服务器事件
3. **添加超时机制**：为事件处理添加超时限制

## 🧪 测试建议

1. 在工作流运行时尝试停止，观察是否能正常响应
2. 检查 ComfyUI 控制台是否有 TaskMonitor 相关错误信息
3. 测试不同类型的工作流（长时间运行、快速完成等）
4. 监控内存和 CPU 使用情况，确认没有资源泄漏

## 📝 总结

TaskMonitor 插件的主要问题在于：
1. **过度拦截系统事件**，可能干扰停止命令
2. **缺少中断事件处理**，无法正确响应停止信号
3. **状态判断逻辑不完善**，可能误判执行状态

建议按照上述解决方案进行修复，确保插件不会干扰 ComfyUI 的正常停止机制。
