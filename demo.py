#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ComfyUI Pet Monitor 演示脚本
用于演示程序的主要功能
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

from config import ConfigManager
from task_monitor_api import TaskMonitorAPI, TaskStatus
from pet_widget import PetWidget
from progress_window import ProgressWindow

class DemoApp(QApplication):
    """演示应用"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        self.config_manager = ConfigManager()
        self.pet_widget = None
        self.progress_window = None
        self.demo_timer = QTimer()
        self.demo_step = 0
        
        self.init_demo()
        
    def init_demo(self):
        """初始化演示"""
        # 创建宠物挂件
        self.pet_widget = PetWidget("meizi")
        self.pet_widget.set_position(500, 300)
        self.pet_widget.double_clicked.connect(self.show_progress_demo)
        self.pet_widget.show()
        
        # 创建进度窗口
        self.progress_window = ProgressWindow()
        
        # 设置演示定时器
        self.demo_timer.timeout.connect(self.demo_step_handler)
        
        # 显示欢迎消息
        QMessageBox.information(
            None,
            "ComfyUI Pet Monitor 演示",
            "欢迎使用 ComfyUI Pet Monitor 演示！\n\n"
            "演示功能：\n"
            "1. 宠物动画状态切换\n"
            "2. 双击宠物显示进度窗口\n"
            "3. 模拟工作流进度更新\n\n"
            "点击确定开始演示..."
        )
        
        # 开始演示
        self.start_demo()
        
    def start_demo(self):
        """开始演示"""
        self.demo_step = 0
        self.demo_timer.start(3000)  # 每3秒切换一次状态
        
    def demo_step_handler(self):
        """演示步骤处理"""
        if self.demo_step == 0:
            # 步骤1: 空闲状态
            print("演示: 空闲状态")
            self.pet_widget.set_task_status(TaskStatus.IDLE)
            self.show_status_message("状态: 空闲", "宠物显示第一帧静态图片")
            
        elif self.demo_step == 1:
            # 步骤2: 运行状态
            print("演示: 运行状态")
            self.pet_widget.set_task_status(TaskStatus.RUNNING)
            self.show_status_message("状态: 运行中", "宠物播放完整动画")
            
        elif self.demo_step == 2:
            # 步骤3: 完成状态
            print("演示: 完成状态")
            self.pet_widget.set_task_status(TaskStatus.COMPLETED)
            self.show_status_message("状态: 已完成", "宠物显示最后一帧静态图片")
            
        elif self.demo_step == 3:
            # 步骤4: 错误状态
            print("演示: 错误状态")
            self.pet_widget.set_task_status(TaskStatus.ERROR)
            self.show_status_message("状态: 错误", "宠物回到静止状态")
            
        elif self.demo_step == 4:
            # 步骤5: 排队状态
            print("演示: 排队状态")
            self.pet_widget.set_task_status(TaskStatus.QUEUED)
            self.show_status_message("状态: 排队中", "宠物播放动画等待执行")
            
        else:
            # 演示结束
            self.demo_timer.stop()
            self.show_final_message()
            return
            
        self.demo_step += 1
        
    def show_status_message(self, status, description):
        """显示状态消息"""
        print(f"{status} - {description}")
        
    def show_progress_demo(self):
        """显示进度演示"""
        if not self.progress_window.isVisible():
            # 模拟进度数据
            demo_progress = {
                "status": "running",
                "task_id": "demo_task_12345",
                "execution_time": 45.6,
                "workflow_progress": {
                    "total_nodes": 10,
                    "executed_nodes": 6,
                    "last_executed_node_id": "6"
                },
                "current_task_progress": {
                    "node_id": "7",
                    "node_type": "KSampler",
                    "step": 15,
                    "total_steps": 20,
                    "text_message": "正在生成图像..."
                },
                "queue": {
                    "running_count": 1,
                    "pending_count": 2
                },
                "error_info": None
            }
            
            pet_pos = self.pet_widget.get_position()
            self.progress_window.show_at_position(pet_pos["x"], pet_pos["y"])
            self.progress_window.update_progress(demo_progress)
            
            print("演示: 显示进度窗口")
        else:
            self.progress_window.hide()
            print("演示: 隐藏进度窗口")
    
    def show_final_message(self):
        """显示最终消息"""
        reply = QMessageBox.question(
            None,
            "演示完成",
            "ComfyUI Pet Monitor 演示完成！\n\n"
            "主要功能已展示：\n"
            "✓ 宠物动画状态切换\n"
            "✓ 双击显示进度窗口\n"
            "✓ 透明背景显示\n"
            "✓ 可拖拽移动\n\n"
            "是否要重新开始演示？",
            QMessageBox.Yes | QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.start_demo()
        else:
            self.quit()

def main():
    """主函数"""
    print("ComfyUI Pet Monitor 演示程序")
    print("=" * 40)
    
    # 创建演示应用
    app = DemoApp(sys.argv)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
