ComfyUI-TaskMonitor是一个监听comfyui工作流程进度的扩展插件，它提供了一个关键的 API 接口，可以获取任务的实时状态和进度信息：
1.API 接口: GET /task_monitor/status
2.返回数据格式: JSON
3.包含信息:
-任务 ID
-状态 (空闲, 运行中, 完成, 错误, 排队中)
-队列信息 (运行中和等待中的任务，包括提示 ID、节点数量、客户端 ID)
-当前任务进度 (节点 ID, 节点类型, 当前步骤, 总步骤, 文本消息)
-工作流进度 (总节点数, 已执行节点数, 最后执行的节点 ID)
-执行时间
-错误信息
desktoppet是一个桌面宠物挂件项目。
现在我需你要结合这两个项目，实现一个桌面小挂件程序监听comfyui工作流进度的功能。
需求如下：
1.默认只显示小挂件，只有用户左键双击小挂件在挂件下方弹出半透明窗体显示工作流进度的详细信息。
2.用户可设置是否需要置顶显示（默认置顶）。
3.宠物挂件使用的图片为透明背景的png图，桌面显示也需要透明背景。
4.宠物挂件人images目录下存放多个宠物的动画序列图片，是给用户可以设置选择不同宠物。
5.允许用户配置 ComfyUI 服务器地址、刷新频率等.
7.工作流未开始时显示第一帧静态图片，开始运行显示完整和动画，运行结束显示最后一帧静态图片。


