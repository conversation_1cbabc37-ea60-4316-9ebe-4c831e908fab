# ComfyUI 桌面宠物监控器

这是一个结合 ComfyUI-TaskMonitor 和 DesktopPet 的项目，旨在创建一个桌面宠物挂件，用于监控 ComfyUI 工作流的进度。

## 项目概述

ComfyUI 桌面宠物监控器是一个可爱的桌面挂件程序，它能够实时监控 ComfyUI 工作流的执行状态，并通过不同的宠物动画来直观地展示当前的工作状态。

## 主要功能

- **桌面宠物挂件**: 在桌面显示可爱的宠物挂件（透明背景）
- **实时监控**: 实时监控 ComfyUI 工作流进度
- **进度详情**: 左键双击宠物时在下方显示半透明窗体，展示详细进度信息
- **用户配置**: 支持用户配置 ComfyUI 服务器地址和刷新频率
- **状态动画**: 根据工作流状态显示不同的宠物动画：
  - 静止状态：工作流未开始时显示第一帧静态图片
  - 运行中：显示完整动画循环
  - 完成状态：显示最后一帧静态图片
- **多宠物支持**: 支持用户选择不同的宠物形象
- **置顶显示**: 提供置顶显示选项（默认开启）
- **系统托盘**: 支持系统托盘操作和管理

## 技术特点

### ComfyUI-TaskMonitor API
- **API 接口**: GET /task_monitor/status
- **返回数据格式**: JSON
- **包含信息**:
  - 任务 ID
  - 状态 (空闲, 运行中, 完成, 错误, 排队中)
  - 队列信息 (运行中和等待中的任务，包括提示 ID、节点数量、客户端 ID)
  - 当前任务进度 (节点 ID, 节点类型, 当前步骤, 总步骤, 文本消息)
  - 工作流进度 (总节点数, 已执行节点数, 最后执行的节点 ID)
  - 执行时间
  - 错误信息

### DesktopPet 特性
- 透明背景的 PNG 图片支持
- 动画序列播放
- 鼠标交互支持
- 可拖拽移动

## 安装和使用

### 环境要求
- Python 3.7+
- PyQt5
- requests

### 安装步骤

1. 克隆或下载项目到本地
2. 确保 ComfyUI 服务器已安装 TaskMonitor 插件
3. 运行程序（选择以下任一方式）：

**方式一：使用启动脚本（推荐）**
```bash
# Windows
python start.py
# 或双击 启动程序.bat

# Linux/Mac
python3 start.py
# 或运行 ./run.sh
```

**方式二：手动安装依赖后运行**
```bash
pip install -r requirements.txt
python comfyui_pet_monitor.py
```

### 使用说明

1. **首次运行**: 程序会在桌面显示默认宠物，并在系统托盘显示图标
2. **查看进度**: 双击宠物挂件可显示/隐藏详细进度窗口
3. **设置配置**: 右键点击宠物或托盘图标，选择"设置"进行配置
4. **更换宠物**: 在设置中可以选择不同的宠物形象
5. **服务器配置**: 在设置中配置 ComfyUI 服务器地址和端口

## 项目结构

```
Desktop_TaskMonitor/
├── comfyui_pet_monitor.py    # 主程序文件
├── config.py                 # 配置管理
├── task_monitor_api.py       # ComfyUI API 接口
├── pet_widget.py            # 宠物挂件组件
├── progress_window.py       # 进度显示窗体
├── settings_dialog.py       # 设置对话框
├── requirements.txt         # 依赖管理
├── config.json             # 配置文件（运行后生成）
├── images/                 # 宠物图片资源
│   ├── meizi/             # 美女宠物
│   ├── Zombie/            # 僵尸宠物
│   ├── WallNut/           # 坚果宠物
│   └── ConeheadZombie/    # 路障僵尸宠物
├── ComfyUI-TaskMonitor/   # ComfyUI 监控插件
└── DesktopPet/           # 原始桌面宠物项目
```

## 配置说明

程序会自动生成 `config.json` 配置文件，包含以下设置：

- **服务器设置**: ComfyUI 服务器地址、端口、协议
- **宠物设置**: 宠物类型、动画速度、大小缩放、位置
- **监控设置**: 刷新间隔、自动隐藏、进度窗口透明度
- **显示设置**: 置顶显示、任务栏显示、拖拽功能

## 开发说明

项目采用模块化设计，主要模块包括：

1. **ConfigManager**: 配置文件管理
2. **TaskMonitorAPI**: ComfyUI API 通信
3. **PetWidget**: 桌面宠物挂件
4. **ProgressWindow**: 进度信息显示
5. **SettingsDialog**: 用户设置界面
6. **ComfyUIPetMonitor**: 主应用程序

## 许可证

本项目基于原有的 ComfyUI-TaskMonitor 和 DesktopPet 项目开发，请遵循相应的开源许可证。


