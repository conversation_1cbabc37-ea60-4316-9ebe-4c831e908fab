@echo off
chcp 65001 >nul
title ComfyUI Pet Monitor

echo ========================================
echo    ComfyUI 桌面宠物监控器
echo ========================================
echo.

REM 检查 Python 是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到 Python，请先安装 Python 3.7+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 检查依赖包...
pip show PyQt5 >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

echo 启动 ComfyUI 桌面宠物监控器...
echo.
python comfyui_pet_monitor.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查错误信息
    pause
)
