#!/bin/bash

echo "========================================"
echo "    ComfyUI 桌面宠物监控器"
echo "========================================"
echo

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到 Python，请先安装 Python 3.7+"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用 Python: $PYTHON_CMD"
$PYTHON_CMD --version

# 检查 pip
if ! command -v pip3 &> /dev/null; then
    if ! command -v pip &> /dev/null; then
        echo "错误: 未找到 pip"
        exit 1
    else
        PIP_CMD="pip"
    fi
else
    PIP_CMD="pip3"
fi

echo "检查依赖包..."
if ! $PYTHON_CMD -c "import PyQt5" &> /dev/null; then
    echo "正在安装依赖包..."
    $PIP_CMD install -r requirements.txt
    if [ $? -ne 0 ]; then
        echo "错误: 依赖包安装失败"
        exit 1
    fi
fi

echo "启动 ComfyUI 桌面宠物监控器..."
echo
$PYTHON_CMD comfyui_pet_monitor.py

if [ $? -ne 0 ]; then
    echo
    echo "程序运行出错，请检查错误信息"
    read -p "按回车键退出..."
fi
