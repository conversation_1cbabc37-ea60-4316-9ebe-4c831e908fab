#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
组件测试脚本
用于测试各个组件是否能正常工作
"""

import sys
import os
from PyQt5.QtWidgets import QApplication

def test_imports():
    """测试导入"""
    print("测试导入...")
    try:
        from config import ConfigManager
        print("✓ ConfigManager 导入成功")
        
        from task_monitor_api import TaskMonitorAPI, TaskStatus
        print("✓ TaskMonitorAPI 导入成功")
        
        from pet_widget import PetWidget
        print("✓ PetWidget 导入成功")
        
        from progress_window import ProgressWindow
        print("✓ ProgressWindow 导入成功")
        
        from settings_dialog import SettingsDialog
        print("✓ SettingsDialog 导入成功")
        
        return True
    except ImportError as e:
        print(f"✗ 导入失败: {e}")
        return False

def test_config():
    """测试配置管理"""
    print("\n测试配置管理...")
    try:
        from config import ConfigManager
        
        config = ConfigManager()
        print(f"✓ 配置文件加载成功")
        
        # 测试获取配置
        url = config.get_comfyui_url()
        print(f"✓ ComfyUI URL: {url}")
        
        pets = config.get_available_pets()
        print(f"✓ 可用宠物: {pets}")
        
        return True
    except Exception as e:
        print(f"✗ 配置测试失败: {e}")
        return False

def test_images():
    """测试图片资源"""
    print("\n测试图片资源...")
    
    images_dir = "images"
    if not os.path.exists(images_dir):
        print(f"✗ 图片目录不存在: {images_dir}")
        return False
    
    pets_found = []
    for item in os.listdir(images_dir):
        item_path = os.path.join(images_dir, item)
        if os.path.isdir(item_path):
            # 检查是否包含PNG图片
            png_files = [f for f in os.listdir(item_path) if f.endswith('.png')]
            if png_files:
                pets_found.append(item)
                print(f"✓ 宠物 {item}: {len(png_files)} 张图片")
    
    if pets_found:
        print(f"✓ 找到 {len(pets_found)} 个宠物")
        return True
    else:
        print("✗ 未找到任何宠物图片")
        return False

def test_qt_components():
    """测试 Qt 组件"""
    print("\n测试 Qt 组件...")
    
    app = QApplication(sys.argv)
    
    try:
        from config import ConfigManager
        from pet_widget import PetWidget
        from progress_window import ProgressWindow
        from settings_dialog import SettingsDialog
        
        # 测试配置管理器
        config = ConfigManager()
        print("✓ ConfigManager 创建成功")
        
        # 测试宠物挂件
        pet = PetWidget("meizi")
        print("✓ PetWidget 创建成功")
        
        # 测试进度窗口
        progress = ProgressWindow()
        print("✓ ProgressWindow 创建成功")
        
        # 测试设置对话框
        settings = SettingsDialog(config)
        print("✓ SettingsDialog 创建成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Qt 组件测试失败: {e}")
        return False
    finally:
        app.quit()

def main():
    """主测试函数"""
    print("ComfyUI Pet Monitor 组件测试")
    print("=" * 40)
    
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("图片资源测试", test_images),
        ("Qt 组件测试", test_qt_components),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n[{test_name}]")
        if test_func():
            passed += 1
            print(f"✓ {test_name} 通过")
        else:
            print(f"✗ {test_name} 失败")
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有测试通过！程序应该可以正常运行。")
        return 0
    else:
        print("✗ 部分测试失败，请检查错误信息。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
