@echo off
title ComfyUI Pet Monitor

echo ========================================
echo    ComfyUI Pet Monitor
echo ========================================
echo.

python --version >nul 2>&1
if errorlevel 1 (
    echo Python not found, please install Python 3.7+
    pause
    exit /b 1
)

echo Checking dependencies...
pip show PyQt5 >nul 2>&1
if errorlevel 1 (
    echo Installing dependencies...
    pip install -r requirements.txt
)

echo Starting program...
python comfyui_pet_monitor.py

if errorlevel 1 (
    echo Program error occurred
    pause
)
