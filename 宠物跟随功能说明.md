# 宠物跟随功能优化说明

## 🎯 新增功能

### 1. 进度窗口跟随宠物移动
- ✅ **实时跟随**: 拖动宠物时，进度窗口会实时跟随移动
- ✅ **平滑移动**: 窗口位置更新流畅，无卡顿
- ✅ **智能定位**: 基于宠物中心点计算窗口位置

### 2. 可配置的窗口偏移量
- ✅ **自定义偏移**: 可设置进度窗口相对于宠物中心点的偏移量
- ✅ **X/Y 独立设置**: 支持水平和垂直方向独立调整
- ✅ **实时生效**: 设置更改后立即生效

### 3. 基于中心点的定位系统
- ✅ **中心点对齐**: 进度窗口基于宠物中心点定位，更加精确
- ✅ **窗口居中**: 进度窗口自动居中对齐到计算位置
- ✅ **尺寸自适应**: 自动考虑窗口大小进行位置调整

## 🔧 技术实现

### 核心组件修改

#### 1. PetWidget (宠物挂件)
```python
# 新增信号
position_changed = pyqtSignal(int, int)  # 位置变化信号

# 新增方法
def get_center_position(self):
    """获取宠物中心点位置"""
    pos = self.pos()
    size = self.size()
    center_x = pos.x() + size.width() // 2
    center_y = pos.y() + size.height() // 2
    return {"x": center_x, "y": center_y}

# 修改鼠标移动事件
def mouseMoveEvent(self, event):
    if self.is_dragging and event.buttons() == Qt.LeftButton:
        new_position = event.globalPos() - self.drag_start_position
        self.move(new_position)
        # 发送位置变化信号
        self.position_changed.emit(new_position.x(), new_position.y())
```

#### 2. ProgressWindow (进度窗口)
```python
# 新增偏移量属性
self.offset_x = 0  # X偏移量
self.offset_y = 50  # Y偏移量

# 新增方法
def show_at_center_position(self, center_x: int, center_y: int):
    """基于宠物中心点显示窗口"""
    window_width = self.width()
    window_height = self.height()
    
    final_x = center_x + self.offset_x - window_width // 2
    final_y = center_y + self.offset_y - window_height // 2
    
    self.move(final_x, final_y)
    self.show()

def update_position_from_pet_center(self, center_x: int, center_y: int):
    """根据宠物中心点更新窗口位置（用于跟随拖动）"""
    if self.isVisible():
        window_width = self.width()
        window_height = self.height()
        
        final_x = center_x + self.offset_x - window_width // 2
        final_y = center_y + self.offset_y - window_height // 2
        
        self.move(final_x, final_y)
```

#### 3. 主程序连接
```python
# 连接位置变化信号
self.pet_widget.position_changed.connect(self.on_pet_position_changed)

def on_pet_position_changed(self, x: int, y: int):
    """宠物位置变化处理"""
    if self.is_progress_window_visible:
        center_pos = self.pet_widget.get_center_position()
        self.progress_window.update_position_from_pet_center(center_pos["x"], center_pos["y"])
```

### 配置系统扩展

#### 配置文件新增项
```json
{
    "monitor_settings": {
        "progress_window_offset": {"x": 0, "y": 50}
    }
}
```

#### 设置界面新增控件
```python
# X偏移量设置
self.offset_x_spin = QSpinBox()
self.offset_x_spin.setRange(-500, 500)
self.offset_x_spin.setValue(0)

# Y偏移量设置  
self.offset_y_spin = QSpinBox()
self.offset_y_spin.setRange(-500, 500)
self.offset_y_spin.setValue(50)
```

## 🎮 使用方法

### 1. 基本操作
1. **显示进度窗口**: 双击宠物挂件
2. **拖动宠物**: 按住左键拖动宠物到新位置
3. **观察跟随**: 进度窗口会自动跟随宠物移动

### 2. 自定义偏移量
1. **打开设置**: 右键宠物 → 设置
2. **切换到监控选项卡**: 找到"进度窗口偏移"设置
3. **调整偏移量**:
   - **X偏移**: 正值向右，负值向左
   - **Y偏移**: 正值向下，负值向上
4. **应用设置**: 点击确定，立即生效

### 3. 测试功能
```bash
python 测试宠物跟随功能.py
```

## 📋 配置示例

### 常用偏移量设置

#### 窗口在宠物下方（默认）
```json
{"x": 0, "y": 50}
```

#### 窗口在宠物上方
```json
{"x": 0, "y": -50}
```

#### 窗口在宠物右侧
```json
{"x": 200, "y": 0}
```

#### 窗口在宠物左侧
```json
{"x": -200, "y": 0}
```

#### 窗口在宠物右下角
```json
{"x": 100, "y": 50}
```

## 🔍 工作原理

### 位置计算逻辑

1. **获取宠物中心点**:
   ```
   center_x = pet_x + pet_width / 2
   center_y = pet_y + pet_height / 2
   ```

2. **计算窗口位置**:
   ```
   window_x = center_x + offset_x - window_width / 2
   window_y = center_y + offset_y - window_height / 2
   ```

3. **实时更新**:
   - 宠物移动时发送 `position_changed` 信号
   - 主程序接收信号并更新进度窗口位置
   - 进度窗口重新计算并移动到新位置

### 信号流程图

```
宠物拖动 → mouseMoveEvent → position_changed 信号
    ↓
主程序接收 → on_pet_position_changed → 获取中心点
    ↓
进度窗口 → update_position_from_pet_center → 重新定位
```

## ✅ 优势特点

1. **用户体验优化**:
   - 进度窗口始终跟随宠物，不会丢失
   - 可自定义窗口位置，适应不同使用习惯
   - 基于中心点定位，更加精确和美观

2. **技术实现优雅**:
   - 使用信号槽机制，解耦合度高
   - 实时响应，性能良好
   - 配置化设计，易于扩展

3. **兼容性良好**:
   - 保持原有功能不变
   - 向后兼容旧配置文件
   - 可选功能，不影响基本使用

## 🧪 测试建议

1. **基本跟随测试**:
   - 显示进度窗口后拖动宠物
   - 观察窗口是否平滑跟随

2. **偏移量测试**:
   - 设置不同的偏移值
   - 验证窗口位置是否正确

3. **边界测试**:
   - 将宠物拖到屏幕边缘
   - 确保进度窗口不会超出屏幕

4. **性能测试**:
   - 快速拖动宠物
   - 观察是否有卡顿或延迟

这些优化大大提升了桌面宠物监控器的用户体验，使其更加实用和美观！
