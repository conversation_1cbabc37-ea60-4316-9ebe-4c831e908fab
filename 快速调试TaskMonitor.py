#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速调试 TaskMonitor 取消事件问题
"""

import os
import shutil
import sys

def find_plugin_path():
    """查找插件路径"""
    possible_paths = [
        "ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "../ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "../../ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "custom_nodes/ComfyUI-TaskMonitor",
        "../custom_nodes/ComfyUI-TaskMonitor",
    ]

    for path in possible_paths:
        if os.path.exists(path):
            return os.path.abspath(path)
    return None

def apply_debug_version():
    """应用调试版本"""
    plugin_path = find_plugin_path()
    if not plugin_path:
        print("❌ 未找到 ComfyUI-TaskMonitor 插件路径")
        return False

    init_file = os.path.join(plugin_path, "__init__.py")
    debug_file = "ComfyUI-TaskMonitor-修复调试版本.py"
    backup_file = os.path.join(plugin_path, "__init__.py.debug_backup")

    if not os.path.exists(debug_file):
        print(f"❌ 修复调试版本文件不存在: {debug_file}")
        return False

    # 备份当前文件
    if os.path.exists(init_file):
        shutil.copy2(init_file, backup_file)
        print(f"✅ 已备份当前文件: {backup_file}")

    # 应用修复调试版本
    shutil.copy2(debug_file, init_file)
    print(f"✅ 已应用修复调试版本: {debug_file} -> {init_file}")

    print("\n🔍 修复调试版本已安装！")
    print("📋 接下来的步骤：")
    print("1. 重启 ComfyUI")
    print("2. 启动一个长时间工作流（比如高分辨率图像生成）")
    print("3. 🚨 在工作流运行过程中点击 '取消当前任务' 按钮")
    print("4. 查看 ComfyUI 控制台输出")
    print("5. 寻找 '🚨🚨🚨 POTENTIAL CANCEL EVENT DETECTED' 消息")
    print("6. 将控制台输出发送给我分析")
    print("\n⚠️  重要：必须在工作流运行时点击取消，而不是等它完成！")

    return True

def apply_final_version():
    """应用最终修复版本"""
    plugin_path = find_plugin_path()
    if not plugin_path:
        print("❌ 未找到 ComfyUI-TaskMonitor 插件路径")
        return False

    init_file = os.path.join(plugin_path, "__init__.py")
    final_file = "ComfyUI-TaskMonitor-最终修复版本.py"
    backup_file = os.path.join(plugin_path, "__init__.py.final_backup")

    if not os.path.exists(final_file):
        print(f"❌ 最终修复版本文件不存在: {final_file}")
        return False

    # 备份当前文件
    if os.path.exists(init_file):
        shutil.copy2(init_file, backup_file)
        print(f"✅ 已备份当前文件: {backup_file}")

    # 应用最终修复版本
    shutil.copy2(final_file, init_file)
    print(f"✅ 已应用最终修复版本: {final_file} -> {init_file}")

    print("\n🎯 最终修复版本已安装！")
    print("📋 基于实际测试结果的修复：")
    print("✅ 确认 execution_interrupted 事件会被完全绕过")
    print("✅ 优化了进度钩子，优先检测中断状态")
    print("✅ 保留完整的监控功能")
    print("\n🚀 现在重启 ComfyUI 并测试取消功能！")

    return True

def restore_backup():
    """恢复备份"""
    plugin_path = find_plugin_path()
    if not plugin_path:
        print("❌ 未找到插件路径")
        return False

    init_file = os.path.join(plugin_path, "__init__.py")
    backup_file = os.path.join(plugin_path, "__init__.py.debug_backup")

    if os.path.exists(backup_file):
        shutil.copy2(backup_file, init_file)
        print(f"✅ 已恢复备份: {backup_file} -> {init_file}")
        return True
    else:
        print(f"❌ 备份文件不存在: {backup_file}")
        return False

def main():
    print("🔍 ComfyUI-TaskMonitor 取消事件调试工具")
    print("=" * 50)

    while True:
        print("\n请选择操作:")
        print("1. 应用调试版本 (捕获所有事件)")
        print("2. 🎯 应用最终修复版本 (基于测试结果)")
        print("3. 恢复原始版本")
        print("0. 退出")

        choice = input("\n请输入选择 (0-3): ").strip()

        if choice == "1":
            if apply_debug_version():
                print("\n⚠️  重要提醒：")
                print("- 调试版本会在控制台输出大量事件信息")
                print("- 请在测试完成后恢复原始版本")
                print("- 重启 ComfyUI 后进行测试")
                break

        elif choice == "2":
            if apply_final_version():
                print("\n🎉 最终修复版本已安装！")
                print("- 基于实际测试发现的 execution_interrupted 事件")
                print("- 应该能够解决工作流无法取消的问题")
                print("- 重启 ComfyUI 后测试取消功能")
                break

        elif choice == "3":
            if restore_backup():
                print("\n✅ 原始版本已恢复，请重启 ComfyUI")
                break

        elif choice == "0":
            print("退出调试工具")
            break

        else:
            print("无效选择，请重新输入")

if __name__ == "__main__":
    main()
