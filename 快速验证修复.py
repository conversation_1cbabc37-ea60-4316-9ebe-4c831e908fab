#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速验证执行时间和图标修复
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

from config import ConfigManager
from pet_widget import PetWidget
from progress_window import ProgressWindow
from task_monitor_api import TaskMonitorAPI, TaskStatus
from settings_dialog import SettingsDialog

def check_icon_files():
    """检查图标文件"""
    print("🔍 检查图标文件...")
    
    icons = [
        ("images/eye.png", "设置图标"),
        ("images/exit.png", "退出图标"),
        ("images/net.png", "网络图标")
    ]
    
    all_exist = True
    for icon_path, icon_name in icons:
        if os.path.exists(icon_path):
            print(f"✅ {icon_name}: {icon_path}")
        else:
            print(f"❌ {icon_name}: {icon_path} (缺失)")
            all_exist = False
    
    return all_exist

def test_execution_time():
    """测试执行时间计算"""
    print("\n⏱️ 测试执行时间计算...")
    
    # 创建任务监控器
    monitor = TaskMonitorAPI("http://127.0.0.1:8188")
    
    # 模拟任务开始
    start_data = {
        "status": "running",
        "task_id": "test_task_123",
        "execution_time": 0,  # 这个值会被我们的计算覆盖
    }
    
    print("📊 模拟任务开始...")
    monitor._handle_status_response(start_data)
    
    # 等待2秒
    time.sleep(2)
    
    # 模拟任务运行中
    running_data = {
        "status": "running", 
        "task_id": "test_task_123",
        "execution_time": 999,  # 错误的时间，应该被覆盖
    }
    
    print("🔄 模拟任务运行中...")
    monitor._handle_status_response(running_data)
    
    # 获取进度信息
    progress_info = monitor.get_progress_info()
    calculated_time = progress_info.get("execution_time", 0)
    
    print(f"📈 计算的执行时间: {calculated_time:.2f}秒")
    
    if 1.8 <= calculated_time <= 2.5:  # 允许一些误差
        print("✅ 执行时间计算正确！")
        return True
    else:
        print(f"❌ 执行时间计算错误！期望约2秒，实际{calculated_time:.2f}秒")
        return False

def test_pet_menu():
    """测试宠物菜单图标"""
    print("\n🐱 测试宠物菜单图标...")
    
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    # 创建宠物挂件
    pet = PetWidget("meizi")
    
    # 模拟右键菜单（不实际显示）
    from PyQt5.QtWidgets import QMenu, QAction
    from PyQt5.QtGui import QIcon
    
    menu = QMenu()
    
    # 测试设置图标
    settings_action = QAction("设置", menu)
    eye_icon_path = os.path.join("images", "eye.png")
    if os.path.exists(eye_icon_path):
        settings_action.setIcon(QIcon(eye_icon_path))
        print("✅ 设置菜单图标加载成功")
    else:
        print("❌ 设置菜单图标加载失败")
    
    # 测试退出图标
    quit_action = QAction("退出", menu)
    exit_icon_path = os.path.join("images", "exit.png")
    if os.path.exists(exit_icon_path):
        quit_action.setIcon(QIcon(exit_icon_path))
        print("✅ 退出菜单图标加载成功")
    else:
        print("❌ 退出菜单图标加载失败")
    
    return True

def test_settings_window():
    """测试设置窗口图标"""
    print("\n⚙️ 测试设置窗口图标...")
    
    app = QApplication.instance()
    if not app:
        app = QApplication(sys.argv)
    
    config_manager = ConfigManager()
    
    # 创建设置对话框（不显示）
    settings = SettingsDialog(config_manager)
    
    # 检查窗口图标
    icon = settings.windowIcon()
    if not icon.isNull():
        print("✅ 设置窗口图标设置成功")
        return True
    else:
        print("❌ 设置窗口图标设置失败")
        return False

def main():
    """主函数"""
    print("🔧 快速验证修复功能")
    print("=" * 50)
    
    results = []
    
    # 检查图标文件
    icon_check = check_icon_files()
    results.append(("图标文件检查", icon_check))
    
    # 测试执行时间
    time_check = test_execution_time()
    results.append(("执行时间计算", time_check))
    
    # 需要 QApplication 的测试
    app = QApplication(sys.argv)
    
    # 测试宠物菜单
    menu_check = test_pet_menu()
    results.append(("宠物菜单图标", menu_check))
    
    # 测试设置窗口
    settings_check = test_settings_window()
    results.append(("设置窗口图标", settings_check))
    
    # 汇总结果
    print("\n📋 测试结果汇总:")
    print("-" * 30)
    
    all_passed = True
    for test_name, passed in results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("-" * 30)
    if all_passed:
        print("🎉 所有测试通过！修复成功！")
        
        # 显示成功消息
        QMessageBox.information(
            None,
            "验证成功",
            "🎉 所有功能验证通过！\n\n"
            "✅ 图标文件存在\n"
            "✅ 执行时间计算正确\n"
            "✅ 菜单图标加载成功\n"
            "✅ 窗口图标设置成功\n\n"
            "现在可以正常使用优化后的功能了！"
        )
    else:
        print("⚠️ 部分测试失败，请检查相关问题")
        
        # 显示失败消息
        failed_tests = [name for name, passed in results if not passed]
        QMessageBox.warning(
            None,
            "验证失败",
            f"⚠️ 以下测试失败：\n\n" + 
            "\n".join(f"❌ {test}" for test in failed_tests) +
            "\n\n请检查相关问题后重新测试。"
        )

if __name__ == "__main__":
    main()
