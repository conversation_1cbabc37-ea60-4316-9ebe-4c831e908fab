# 执行时间和图标优化说明

## 🐛 问题修复

### 1. 执行时间计算不准确问题

#### 问题描述
- 显示的执行时间与实际工作流运行时间差距较大
- 执行时间可能显示为 0 或异常值

#### 根本原因
- 原来依赖 TaskMonitor API 返回的执行时间
- TaskMonitor API 的执行时间计算可能不准确或延迟
- 客户端没有自己的时间跟踪机制

#### 解决方案
在 `TaskMonitorAPI` 类中添加客户端时间跟踪：

```python
# 新增属性
self.execution_start_time = None
self.current_execution_time = 0
self.last_task_id = None

# 状态处理逻辑
def _handle_status_response(self, data):
    current_task_id = data.get("task_id")
    current_status = TaskStatus(status_str)
    
    # 检查任务是否变化
    if current_task_id != self.last_task_id:
        self.last_task_id = current_task_id
        if current_status == TaskStatus.RUNNING:
            # 新任务开始 - 记录开始时间
            self.execution_start_time = time.time()
            self.current_execution_time = 0
    
    # 实时计算执行时间
    if self.execution_start_time and current_status == TaskStatus.RUNNING:
        self.current_execution_time = time.time() - self.execution_start_time
    elif current_status in [TaskStatus.COMPLETED, TaskStatus.ERROR]:
        # 任务完成 - 保持最后的执行时间
        if self.execution_start_time:
            self.current_execution_time = time.time() - self.execution_start_time
            self.execution_start_time = None
    
    # 更新数据，使用我们计算的执行时间
    updated_data = data.copy()
    updated_data["execution_time"] = self.current_execution_time
```

#### 优化效果
- ✅ **精确计算**: 基于客户端时间戳，精确到毫秒
- ✅ **实时更新**: 运行中实时显示当前执行时间
- ✅ **状态感知**: 根据任务状态智能处理时间计算
- ✅ **任务切换**: 正确处理多个任务的时间跟踪

## 🎨 界面优化

### 2. 右键菜单图标

#### 优化内容
为宠物右键菜单添加图标：
- **设置菜单**: 使用 `images/eye.png`
- **退出菜单**: 使用 `images/exit.png`

#### 实现代码
```python
def show_context_menu(self, position):
    menu = QMenu(self)
    
    # 设置菜单项
    settings_action = QAction("设置", self)
    eye_icon_path = os.path.join("images", "eye.png")
    if os.path.exists(eye_icon_path):
        settings_action.setIcon(QIcon(eye_icon_path))
    
    # 退出菜单项
    quit_action = QAction("退出", self)
    exit_icon_path = os.path.join("images", "exit.png")
    if os.path.exists(exit_icon_path):
        quit_action.setIcon(QIcon(exit_icon_path))
```

### 3. 设置窗口图标

#### 优化内容
为设置对话框标题栏添加自定义图标：
- **窗口图标**: 使用 `images/net.png`

#### 实现代码
```python
def init_ui(self):
    self.setWindowTitle("ComfyUI 宠物监控 - 设置")
    
    # 设置窗口图标
    net_icon_path = os.path.join("images", "net.png")
    if os.path.exists(net_icon_path):
        self.setWindowIcon(QIcon(net_icon_path))
```

## 📁 图标文件要求

### 图标文件列表
```
images/
├── eye.png     # 设置菜单图标
├── exit.png    # 退出菜单图标
└── net.png     # 设置窗口图标
```

### 图标规格建议
- **格式**: PNG（支持透明背景）
- **尺寸**: 16x16 或 24x24 像素（菜单图标）
- **尺寸**: 32x32 像素（窗口图标）
- **风格**: 简洁、清晰、符合功能含义

### 容错处理
```python
# 图标文件不存在时的处理
if os.path.exists(icon_path):
    action.setIcon(QIcon(icon_path))
# 如果文件不存在，使用默认图标（无图标）
```

## 🧪 测试方法

### 执行时间测试
```bash
python 测试执行时间和图标.py
```

**测试内容**：
1. 模拟任务运行，观察执行时间是否准确
2. 检查时间是否实时更新
3. 验证任务完成后时间是否保持

### 图标显示测试
1. **右键菜单图标**：
   - 右键点击宠物
   - 检查"设置"和"退出"菜单项是否有图标

2. **设置窗口图标**：
   - 右键点击宠物 → 设置
   - 检查设置窗口标题栏是否显示自定义图标

### 完整功能测试
```bash
python comfyui_pet_monitor.py
```

## 📊 性能影响

### 执行时间计算
- **CPU 开销**: 极低（仅时间戳计算）
- **内存开销**: 忽略不计（几个变量）
- **更新频率**: 跟随监控刷新间隔（默认1秒）

### 图标加载
- **启动时间**: 轻微增加（加载图标文件）
- **内存使用**: 少量增加（图标缓存）
- **运行性能**: 无影响

## 🔧 技术细节

### 时间计算逻辑
```
任务开始 → 记录 start_time
运行中   → current_time = now() - start_time
完成     → final_time = now() - start_time, 清除 start_time
空闲     → 重置所有时间变量
```

### 状态转换处理
```
IDLE → RUNNING: 开始计时
RUNNING → RUNNING: 更新时间
RUNNING → COMPLETED: 保存最终时间
RUNNING → ERROR: 保存最终时间
COMPLETED/ERROR → IDLE: 重置时间
```

### 图标加载策略
```
1. 检查文件是否存在
2. 存在 → 加载并设置图标
3. 不存在 → 使用默认外观（无图标）
4. 错误处理 → 静默失败，不影响功能
```

## ✅ 优化效果总结

### 用户体验提升
1. **准确的执行时间**: 用户可以准确了解任务运行时长
2. **美观的界面**: 图标让界面更加专业和美观
3. **一致的视觉风格**: 统一的图标风格提升整体体验

### 技术改进
1. **客户端时间跟踪**: 不依赖服务器时间，更加可靠
2. **容错设计**: 图标文件缺失不影响功能
3. **性能优化**: 最小的性能开销，最大的用户体验提升

### 兼容性保证
1. **向后兼容**: 不影响现有功能
2. **可选功能**: 图标文件可选，不是必需的
3. **优雅降级**: 图标加载失败时使用默认外观

这些优化让桌面宠物监控器更加精确、美观和专业！
