# ComfyUI Pet Monitor 故障排除指南

## 常见问题及解决方案

### 1. 启动脚本问题

#### 问题：run.bat 出现乱码或无法运行
**症状**：批处理文件显示乱码字符，如 '潰瀹犵墿鐩戞帶鍣?echo'

**解决方案**：
1. **推荐方式**：直接使用 Python 启动脚本
   ```bash
   python start.py
   ```

2. **或者使用简化的批处理文件**：
   - 双击 `启动程序.bat`（无中文字符版本）

3. **手动启动**：
   ```bash
   pip install -r requirements.txt
   python comfyui_pet_monitor.py
   ```

#### 问题：提示 Python 未找到
**解决方案**：
1. 安装 Python 3.7+ 版本：https://www.python.org/downloads/
2. 确保 Python 已添加到系统 PATH 环境变量
3. 重启命令行窗口

### 2. 依赖安装问题

#### 问题：PyQt5 安装失败
**解决方案**：
1. 更新 pip：
   ```bash
   python -m pip install --upgrade pip
   ```

2. 使用国内镜像源：
   ```bash
   pip install -i https://pypi.tuna.tsinghua.edu.cn/simple PyQt5
   ```

3. 如果仍然失败，尝试安装预编译版本：
   ```bash
   pip install PyQt5-tools
   ```

#### 问题：requests 模块缺失
**解决方案**：
```bash
pip install requests
```

### 3. 程序运行问题

#### 问题：程序启动后没有显示宠物
**可能原因及解决方案**：
1. **图片资源缺失**：
   - 确保 `images` 目录存在
   - 运行测试脚本检查：`python test_components.py`

2. **宠物位置超出屏幕**：
   - 删除 `config.json` 文件重置位置
   - 或在设置中调整宠物位置

3. **系统托盘不支持**：
   - 检查系统是否支持托盘功能
   - 尝试在任务管理器中查看程序是否运行

#### 问题：无法连接到 ComfyUI 服务器
**解决方案**：
1. **检查服务器设置**：
   - 右键宠物 → 设置 → 服务器选项卡
   - 确认主机地址和端口正确（默认：127.0.0.1:8188）

2. **测试连接**：
   - 在设置对话框中点击"测试连接"按钮
   - 确保 ComfyUI 服务器正在运行

3. **检查 TaskMonitor 插件**：
   - 确保 ComfyUI 已安装 TaskMonitor 插件
   - 访问 http://127.0.0.1:8188/task_monitor/status 测试 API

#### 问题：宠物动画不播放
**解决方案**：
1. 检查动画设置：
   - 右键宠物 → 设置 → 宠物选项卡
   - 调整动画速度

2. 检查图片文件：
   - 确保宠物目录包含完整的序列图片
   - 图片命名格式：`宠物名_数字.png`

### 4. 性能问题

#### 问题：程序占用内存过高
**解决方案**：
1. 调整刷新间隔：
   - 设置 → 监控 → 增加刷新间隔时间

2. 减少动画帧数：
   - 删除部分不必要的动画帧图片

#### 问题：程序响应缓慢
**解决方案**：
1. 检查网络连接到 ComfyUI 服务器
2. 降低图片分辨率
3. 关闭不必要的后台程序

### 5. 配置问题

#### 问题：设置无法保存
**解决方案**：
1. 检查文件权限：
   - 确保程序目录有写入权限

2. 手动编辑配置：
   - 编辑 `config.json` 文件
   - 确保 JSON 格式正确

#### 问题：配置文件损坏
**解决方案**：
1. 删除 `config.json` 文件
2. 重启程序，将自动生成默认配置

### 6. 系统兼容性问题

#### Windows 系统
- 支持 Windows 7 及以上版本
- 需要安装 Visual C++ Redistributable

#### Linux 系统
- 需要安装 Qt5 开发库：
  ```bash
  sudo apt-get install python3-pyqt5
  ```

#### macOS 系统
- 需要 macOS 10.12 及以上版本
- 可能需要允许程序在安全设置中运行

## 调试方法

### 1. 运行测试脚本
```bash
python test_components.py
```

### 2. 运行演示模式
```bash
python demo.py
```

### 3. 查看详细错误信息
在命令行中运行程序可以看到详细的错误信息：
```bash
python comfyui_pet_monitor.py
```

### 4. 检查日志
程序会在控制台输出运行状态信息，注意查看错误提示。

## 获取帮助

如果以上解决方案都无法解决问题，请：

1. 运行 `python test_components.py` 并提供测试结果
2. 提供详细的错误信息和系统环境
3. 说明具体的操作步骤和期望结果

## 常用命令总结

```bash
# 测试组件
python test_components.py

# 启动程序
python start.py

# 演示模式
python demo.py

# 直接启动（需要先安装依赖）
python comfyui_pet_monitor.py

# 安装依赖
pip install -r requirements.txt

# 更新依赖
pip install --upgrade PyQt5 requests
```
