#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试宠物跟随功能
"""

import sys
from PyQt5.QtWidgets import QApplication, QMessageBox
from PyQt5.QtCore import QTimer

from config import ConfigManager
from pet_widget import PetWidget
from progress_window import ProgressWindow

class TestFollowApp(QApplication):
    """测试跟随功能的应用"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        self.config_manager = ConfigManager()
        self.pet_widget = None
        self.progress_window = None
        self.is_progress_visible = False
        
        self.init_test()
        
    def init_test(self):
        """初始化测试"""
        # 创建宠物挂件
        self.pet_widget = PetWidget("meizi")
        self.pet_widget.set_position(500, 300)
        self.pet_widget.double_clicked.connect(self.toggle_progress_window)
        self.pet_widget.position_changed.connect(self.on_pet_position_changed)
        self.pet_widget.show()
        
        # 创建进度窗口
        self.progress_window = ProgressWindow()
        self.progress_window.close_requested.connect(self.hide_progress_window)
        
        # 设置测试偏移量
        self.progress_window.set_offset(0, 80)  # 在宠物下方80像素
        
        # 显示说明
        QMessageBox.information(
            None,
            "宠物跟随功能测试",
            "测试功能：\n\n"
            "1. 双击宠物显示/隐藏进度窗口\n"
            "2. 拖动宠物时，进度窗口会跟随移动\n"
            "3. 进度窗口位置基于宠物中心点 + 偏移量\n\n"
            "操作说明：\n"
            "- 双击宠物：显示进度窗口\n"
            "- 拖动宠物：观察进度窗口跟随\n"
            "- 右键宠物：退出测试\n\n"
            "点击确定开始测试..."
        )
        
        # 模拟进度数据
        self.update_demo_progress()
        
    def toggle_progress_window(self):
        """切换进度窗口显示"""
        if self.is_progress_visible:
            self.hide_progress_window()
        else:
            self.show_progress_window()
    
    def show_progress_window(self):
        """显示进度窗口"""
        if not self.is_progress_visible:
            center_pos = self.pet_widget.get_center_position()
            self.progress_window.show_at_center_position(center_pos["x"], center_pos["y"])
            self.is_progress_visible = True
            print(f"显示进度窗口 - 宠物中心: ({center_pos['x']}, {center_pos['y']})")
    
    def hide_progress_window(self):
        """隐藏进度窗口"""
        if self.is_progress_visible:
            self.progress_window.hide()
            self.is_progress_visible = False
            print("隐藏进度窗口")
    
    def on_pet_position_changed(self, x: int, y: int):
        """宠物位置变化处理"""
        if self.is_progress_visible:
            center_pos = self.pet_widget.get_center_position()
            self.progress_window.update_position_from_pet_center(center_pos["x"], center_pos["y"])
            print(f"更新进度窗口位置 - 宠物中心: ({center_pos['x']}, {center_pos['y']})")
    
    def update_demo_progress(self):
        """更新演示进度数据"""
        demo_progress = {
            "status": "running",
            "task_id": "test_task_12345",
            "execution_time": 25.6,
            "workflow_progress": {
                "total_nodes": 8,
                "executed_nodes": 5,
                "last_executed_node_id": "5"
            },
            "current_task_progress": {
                "node_id": "6",
                "node_type": "KSampler",
                "step": 12,
                "total_steps": 20,
                "text_message": "正在生成图像..."
            },
            "queue": {
                "running_count": 1,
                "pending_count": 0
            },
            "is_interrupted": False,
        }
        
        self.progress_window.update_progress(demo_progress)

def main():
    """主函数"""
    print("宠物跟随功能测试程序")
    print("=" * 40)
    
    # 创建测试应用
    app = TestFollowApp(sys.argv)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
