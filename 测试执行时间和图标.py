#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试执行时间计算和图标显示功能
"""

import sys
import os
import time
from PyQt5.QtWidgets import QApplication, QMessageBox, QVBoxLayout, QWidget, QLabel, QPushButton
from PyQt5.QtCore import QTimer
from PyQt5.QtGui import QIcon

from config import ConfigManager
from pet_widget import PetWidget
from progress_window import ProgressWindow
from task_monitor_api import TaskMonitorAPI, TaskStatus
from settings_dialog import SettingsDialog

class TestApp(QApplication):
    """测试应用"""
    
    def __init__(self, argv):
        super().__init__(argv)
        
        self.config_manager = ConfigManager()
        self.pet_widget = None
        self.progress_window = None
        self.task_monitor = None
        self.settings_dialog = None
        
        self.test_start_time = None
        self.test_timer = QTimer()
        self.test_timer.timeout.connect(self.update_test_progress)
        
        self.init_test()
        
    def init_test(self):
        """初始化测试"""
        # 检查图标文件是否存在
        self.check_icon_files()
        
        # 创建宠物挂件
        self.pet_widget = PetWidget("meizi")
        self.pet_widget.set_position(500, 300)
        self.pet_widget.double_clicked.connect(self.toggle_progress_window)
        self.pet_widget.settings_requested.connect(self.show_settings)
        self.pet_widget.quit_requested.connect(self.quit)
        self.pet_widget.show()
        
        # 创建进度窗口
        self.progress_window = ProgressWindow()
        self.progress_window.set_offset(0, 80)
        
        # 创建模拟的任务监控器
        self.task_monitor = TaskMonitorAPI("http://127.0.0.1:8188")
        
        # 显示说明
        QMessageBox.information(
            None,
            "执行时间和图标测试",
            "测试功能：\n\n"
            "1. 执行时间计算修复\n"
            "2. 右键菜单图标显示\n"
            "3. 设置窗口图标显示\n\n"
            "操作说明：\n"
            "- 双击宠物：显示进度窗口（观察执行时间）\n"
            "- 右键宠物：查看菜单图标\n"
            "- 点击设置：查看窗口图标\n\n"
            "点击确定开始测试..."
        )
        
        # 开始模拟任务
        self.start_test_task()
        
    def check_icon_files(self):
        """检查图标文件"""
        icons = [
            ("images/eye.png", "设置图标"),
            ("images/exit.png", "退出图标"),
            ("images/net.png", "网络图标")
        ]
        
        missing_icons = []
        for icon_path, icon_name in icons:
            if not os.path.exists(icon_path):
                missing_icons.append(f"{icon_name}: {icon_path}")
        
        if missing_icons:
            QMessageBox.warning(
                None,
                "图标文件缺失",
                f"以下图标文件不存在：\n\n" + "\n".join(missing_icons) + 
                "\n\n菜单和窗口将使用默认图标。"
            )
        else:
            print("✅ 所有图标文件都存在")
    
    def start_test_task(self):
        """开始测试任务"""
        self.test_start_time = time.time()
        self.test_timer.start(1000)  # 每秒更新一次
        print("🚀 开始模拟任务执行...")
        
    def update_test_progress(self):
        """更新测试进度"""
        if not self.test_start_time:
            return
            
        elapsed_time = time.time() - self.test_start_time
        
        # 模拟不同阶段的进度
        if elapsed_time < 10:
            # 前10秒：运行中
            step = int(elapsed_time * 2)  # 每秒增加2步
            total_steps = 20
            
            demo_progress = {
                "status": "running",
                "task_id": f"test_task_{int(self.test_start_time)}",
                "execution_time": elapsed_time,  # 这里会被 TaskMonitorAPI 重新计算
                "workflow_progress": {
                    "total_nodes": 8,
                    "executed_nodes": min(int(elapsed_time / 1.25), 8),
                    "last_executed_node_id": str(min(int(elapsed_time / 1.25), 8))
                },
                "current_task_progress": {
                    "node_id": "6",
                    "node_type": "KSampler",
                    "step": min(step, total_steps),
                    "total_steps": total_steps,
                    "text_message": f"正在生成图像... ({elapsed_time:.1f}s)"
                },
                "queue": {
                    "running_count": 1,
                    "pending_count": 0
                },
                "is_interrupted": False,
            }
        elif elapsed_time < 15:
            # 10-15秒：完成
            demo_progress = {
                "status": "completed",
                "task_id": f"test_task_{int(self.test_start_time)}",
                "execution_time": elapsed_time,
                "workflow_progress": {
                    "total_nodes": 8,
                    "executed_nodes": 8,
                    "last_executed_node_id": "8"
                },
                "current_task_progress": None,
                "queue": {
                    "running_count": 0,
                    "pending_count": 0
                },
                "is_interrupted": False,
            }
        else:
            # 15秒后：空闲，重新开始
            self.test_start_time = time.time()
            demo_progress = {
                "status": "idle",
                "task_id": None,
                "execution_time": 0,
                "workflow_progress": {
                    "total_nodes": 0,
                    "executed_nodes": 0,
                    "last_executed_node_id": None
                },
                "current_task_progress": None,
                "queue": {
                    "running_count": 0,
                    "pending_count": 0
                },
                "is_interrupted": False,
            }
            print("🔄 重新开始模拟任务...")
        
        # 模拟 TaskMonitorAPI 的处理
        self.task_monitor._handle_status_response(demo_progress)
        
        # 更新进度窗口
        if hasattr(self, 'is_progress_visible') and self.is_progress_visible:
            updated_data = self.task_monitor.get_progress_info()
            self.progress_window.update_progress(updated_data)
            print(f"📊 更新进度 - 状态: {updated_data['status']}, 执行时间: {updated_data['execution_time']:.1f}s")
    
    def toggle_progress_window(self):
        """切换进度窗口显示"""
        if hasattr(self, 'is_progress_visible') and self.is_progress_visible:
            self.hide_progress_window()
        else:
            self.show_progress_window()
    
    def show_progress_window(self):
        """显示进度窗口"""
        center_pos = self.pet_widget.get_center_position()
        self.progress_window.show_at_center_position(center_pos["x"], center_pos["y"])
        self.is_progress_visible = True
        
        # 立即更新进度信息
        progress_data = self.task_monitor.get_progress_info()
        self.progress_window.update_progress(progress_data)
        print("👁️ 显示进度窗口")
    
    def hide_progress_window(self):
        """隐藏进度窗口"""
        self.progress_window.hide()
        self.is_progress_visible = False
        print("🙈 隐藏进度窗口")
    
    def show_settings(self):
        """显示设置对话框"""
        if not self.settings_dialog:
            self.settings_dialog = SettingsDialog(self.config_manager)
        
        print("⚙️ 显示设置对话框（检查窗口图标）")
        self.settings_dialog.exec_()

def main():
    """主函数"""
    print("执行时间和图标测试程序")
    print("=" * 40)
    
    # 创建测试应用
    app = TestApp(sys.argv)
    
    # 运行应用
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
