#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ComfyUI-TaskMonitor 紧急修复脚本
用于快速禁用或修复 TaskMonitor 插件
"""

import os
import shutil
import sys
from pathlib import Path

def find_comfyui_path():
    """查找 ComfyUI 路径"""
    possible_paths = [
        "ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "../ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "../../ComfyUI/custom_nodes/ComfyUI-TaskMonitor",
        "custom_nodes/ComfyUI-TaskMonitor",
        "../custom_nodes/ComfyUI-TaskMonitor",
    ]
    
    for path in possible_paths:
        if os.path.exists(path):
            return os.path.abspath(path)
    
    return None

def backup_original(plugin_path):
    """备份原始文件"""
    init_file = os.path.join(plugin_path, "__init__.py")
    backup_file = os.path.join(plugin_path, "__init__.py.backup")
    
    if os.path.exists(init_file) and not os.path.exists(backup_file):
        shutil.copy2(init_file, backup_file)
        print(f"✓ 已备份原始文件: {backup_file}")
        return True
    elif os.path.exists(backup_file):
        print(f"✓ 备份文件已存在: {backup_file}")
        return True
    else:
        print(f"✗ 未找到原始文件: {init_file}")
        return False

def disable_plugin(plugin_path):
    """禁用插件"""
    init_file = os.path.join(plugin_path, "__init__.py")
    disabled_file = os.path.join(plugin_path, "__init__.py.disabled")
    
    if os.path.exists(init_file):
        shutil.move(init_file, disabled_file)
        print(f"✓ 插件已禁用: {init_file} -> {disabled_file}")
        return True
    else:
        print(f"✗ 插件文件不存在: {init_file}")
        return False

def enable_plugin(plugin_path):
    """启用插件"""
    init_file = os.path.join(plugin_path, "__init__.py")
    disabled_file = os.path.join(plugin_path, "__init__.py.disabled")
    
    if os.path.exists(disabled_file):
        shutil.move(disabled_file, init_file)
        print(f"✓ 插件已启用: {disabled_file} -> {init_file}")
        return True
    else:
        print(f"✗ 禁用文件不存在: {disabled_file}")
        return False

def apply_safe_version(plugin_path):
    """应用安全版本"""
    init_file = os.path.join(plugin_path, "__init__.py")
    safe_file = "ComfyUI-TaskMonitor-安全版本.py"
    
    if not os.path.exists(safe_file):
        print(f"✗ 安全版本文件不存在: {safe_file}")
        return False
    
    # 备份当前文件
    backup_original(plugin_path)
    
    # 复制安全版本
    shutil.copy2(safe_file, init_file)
    print(f"✓ 已应用安全版本: {safe_file} -> {init_file}")
    return True

def restore_backup(plugin_path):
    """恢复备份"""
    init_file = os.path.join(plugin_path, "__init__.py")
    backup_file = os.path.join(plugin_path, "__init__.py.backup")
    
    if os.path.exists(backup_file):
        shutil.copy2(backup_file, init_file)
        print(f"✓ 已恢复备份: {backup_file} -> {init_file}")
        return True
    else:
        print(f"✗ 备份文件不存在: {backup_file}")
        return False

def show_status(plugin_path):
    """显示插件状态"""
    init_file = os.path.join(plugin_path, "__init__.py")
    disabled_file = os.path.join(plugin_path, "__init__.py.disabled")
    backup_file = os.path.join(plugin_path, "__init__.py.backup")
    
    print(f"\n插件路径: {plugin_path}")
    print(f"主文件 (__init__.py): {'存在' if os.path.exists(init_file) else '不存在'}")
    print(f"禁用文件 (__init__.py.disabled): {'存在' if os.path.exists(disabled_file) else '不存在'}")
    print(f"备份文件 (__init__.py.backup): {'存在' if os.path.exists(backup_file) else '不存在'}")
    
    if os.path.exists(init_file):
        # 检查文件内容判断版本
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if "Safe Mode" in content:
                print("当前版本: 安全版本")
            elif "Fixed" in content:
                print("当前版本: 修复版本")
            else:
                print("当前版本: 原始版本")
    
    print()

def main():
    """主函数"""
    print("ComfyUI-TaskMonitor 紧急修复工具")
    print("=" * 50)
    
    # 查找插件路径
    plugin_path = find_comfyui_path()
    if not plugin_path:
        print("✗ 未找到 ComfyUI-TaskMonitor 插件路径")
        print("请确保在正确的目录运行此脚本")
        return
    
    print(f"找到插件路径: {plugin_path}")
    
    while True:
        show_status(plugin_path)
        
        print("请选择操作:")
        print("1. 禁用插件 (立即解决停止问题)")
        print("2. 启用插件")
        print("3. 应用安全版本 (保留 API 功能)")
        print("4. 恢复原始备份")
        print("5. 显示状态")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-5): ").strip()
        
        if choice == "1":
            if disable_plugin(plugin_path):
                print("\n✅ 插件已禁用！请重启 ComfyUI 以生效。")
                print("现在工作流应该可以正常停止了。")
        
        elif choice == "2":
            if enable_plugin(plugin_path):
                print("\n✅ 插件已启用！请重启 ComfyUI 以生效。")
        
        elif choice == "3":
            if apply_safe_version(plugin_path):
                print("\n✅ 安全版本已应用！请重启 ComfyUI 以生效。")
                print("这个版本只提供 API 功能，不会干扰工作流停止。")
        
        elif choice == "4":
            if restore_backup(plugin_path):
                print("\n✅ 原始备份已恢复！请重启 ComfyUI 以生效。")
        
        elif choice == "5":
            continue
        
        elif choice == "0":
            print("退出修复工具")
            break
        
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()
